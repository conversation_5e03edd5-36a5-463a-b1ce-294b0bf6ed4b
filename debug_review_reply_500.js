/**
 * 调试评价回复500错误
 * 
 * 错误详情:
 * - 端点: POST /admin/reviews/14/reply
 * - 参数: reply=%E5%A4%A7+kjksjjdjasdjajd (URL编码的中文+随机文本)
 * - 状态: 500 Internal Server Error
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8080/api';

async function debugReviewReply500() {
    console.log('🔍 调试评价回复500错误');
    console.log('===================');
    
    try {
        // 1. 首先检查评价ID 14是否存在
        console.log('\n1️⃣ 检查评价ID 14是否存在...');
        try {
            const reviewResponse = await axios.get(`${BASE_URL}/reviews/14`);
            console.log('✅ 评价ID 14存在');
            console.log('📋 评价详情:', {
                id: reviewResponse.data.data.id,
                status: reviewResponse.data.data.status,
                rating: reviewResponse.data.data.rating,
                comment: reviewResponse.data.data.comment?.substring(0, 50) + '...',
                adminReply: reviewResponse.data.data.adminReply,
                hotelId: reviewResponse.data.data.hotelId,
                userId: reviewResponse.data.data.userId
            });
            
            // 检查状态是否为APPROVED
            if (reviewResponse.data.data.status !== 'APPROVED') {
                console.log('⚠️  评价状态不是APPROVED，当前状态:', reviewResponse.data.data.status);
                console.log('💡 只有APPROVED状态的评价才能被回复');
                
                // 如果是PENDING状态，先审核通过
                if (reviewResponse.data.data.status === 'PENDING') {
                    console.log('\n🔧 尝试先审核通过评价...');
                    try {
                        await axios.post(`${BASE_URL}/admin/reviews/14/approve`, null, {
                            params: { approved: true }
                        });
                        console.log('✅ 评价审核通过');
                    } catch (approveError) {
                        console.log('❌ 审核失败:', approveError.response?.data || approveError.message);
                    }
                }
            }
            
        } catch (error) {
            if (error.response?.status === 404) {
                console.log('❌ 评价ID 14不存在');
                return;
            } else {
                console.log('❌ 获取评价详情失败:', error.response?.data || error.message);
                return;
            }
        }
        
        // 2. 测试不同的回复内容格式
        console.log('\n2️⃣ 测试不同的回复内容格式...');
        
        const testCases = [
            {
                name: '简单英文回复',
                reply: 'Thank you for your review!'
            },
            {
                name: '简单中文回复',
                reply: '感谢您的评价！'
            },
            {
                name: '原始问题回复（URL解码后）',
                reply: '大 kjksjjdjasdjajd'
            },
            {
                name: '长中文回复',
                reply: '感谢您的宝贵评价，我们会继续努力提供更好的服务体验！'
            }
        ];
        
        for (const testCase of testCases) {
            console.log(`\n🧪 测试: ${testCase.name}`);
            console.log(`📝 回复内容: "${testCase.reply}"`);
            
            try {
                // 使用form-urlencoded格式（与前端一致）
                const response = await axios.post(
                    `${BASE_URL}/admin/reviews/14/reply`,
                    `reply=${encodeURIComponent(testCase.reply)}`,
                    {
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    }
                );
                
                console.log('✅ 回复成功');
                console.log('📋 响应数据:', {
                    success: response.data.success,
                    message: response.data.message,
                    adminReply: response.data.data?.adminReply
                });
                
                // 成功后跳出循环
                break;
                
            } catch (error) {
                console.log('❌ 回复失败');
                console.log('🔍 错误详情:', {
                    status: error.response?.status,
                    statusText: error.response?.statusText,
                    data: error.response?.data,
                    message: error.message
                });
                
                // 如果是500错误，记录详细信息
                if (error.response?.status === 500) {
                    console.log('🚨 500错误详细分析:');
                    console.log('   - URL编码后的回复:', encodeURIComponent(testCase.reply));
                    console.log('   - 请求头:', error.config?.headers);
                    console.log('   - 请求体:', error.config?.data);
                }
            }
        }
        
        // 3. 测试使用JSON格式（备选方案）
        console.log('\n3️⃣ 测试JSON格式请求...');
        try {
            const response = await axios.post(
                `${BASE_URL}/admin/reviews/14/reply`,
                { reply: '测试JSON格式回复' },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );
            console.log('✅ JSON格式回复成功');
        } catch (error) {
            console.log('❌ JSON格式回复失败:', error.response?.data || error.message);
        }
        
        // 4. 检查服务器日志建议
        console.log('\n4️⃣ 服务器日志检查建议:');
        console.log('📋 请检查以下日志文件:');
        console.log('   - Spring Boot应用日志');
        console.log('   - 数据库错误日志');
        console.log('   - Tomcat/服务器错误日志');
        console.log('\n🔍 关键日志搜索关键词:');
        console.log('   - "管理员回复评价，ID: 14"');
        console.log('   - "回复评价失败"');
        console.log('   - "IllegalArgumentException"');
        console.log('   - "SQLException"');
        console.log('   - "ConstraintViolationException"');
        
    } catch (error) {
        console.error('❌ 调试过程中发生错误:', error.message);
        if (error.response) {
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行调试
debugReviewReply500().catch(console.error);
