import api from './api';

/**
 * 套餐付款服务
 */
export const packagePaymentService = {
  /**
   * 创建套餐付款
   * @param {number} bookingId - 预订ID
   * @param {Object} paymentData - 付款数据
   * @param {number} paymentData.amount - 付款金额
   * @param {string} paymentData.currency - 货币类型
   * @param {string} paymentData.paymentMethod - 付款方式
   * @param {string} paymentData.gatewayType - 网关类型
   * @param {string} paymentData.returnUrl - 返回URL
   * @param {string} paymentData.notifyUrl - 通知URL
   * @param {string} paymentData.remarks - 备注
   * @returns {Promise} 付款结果
   */
  createPayment: async (bookingId, paymentData) => {
    try {
      const response = await api.post(`/packages/bookings/${bookingId}/payment`, paymentData);
      return response;
    } catch (error) {
      console.error('Create package payment error:', error);
      throw error;
    }
  },

  /**
   * 处理付款
   * @param {string} paymentNumber - 付款编号
   * @returns {Promise} 处理结果
   */
  processPayment: async (paymentNumber) => {
    try {
      const response = await api.post(`/packages/payments/${paymentNumber}/process`);
      return response;
    } catch (error) {
      console.error('Process package payment error:', error);
      throw error;
    }
  },

  /**
   * 查询付款记录
   * @param {string} paymentNumber - 付款编号
   * @returns {Promise} 付款记录
   */
  getPayment: async (paymentNumber) => {
    try {
      const response = await api.get(`/packages/payments/${paymentNumber}`);
      return response;
    } catch (error) {
      console.error('Get package payment error:', error);
      throw error;
    }
  },

  /**
   * 获取预订的付款记录
   * @param {number} bookingId - 预订ID
   * @returns {Promise} 付款记录列表
   */
  getBookingPayments: async (bookingId) => {
    try {
      const response = await api.get(`/packages/bookings/${bookingId}/payments`);
      return response;
    } catch (error) {
      console.error('Get booking payments error:', error);
      throw error;
    }
  },

  /**
   * 获取用户的付款记录
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页大小
   * @returns {Promise} 付款记录分页数据
   */
  getUserPayments: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
          queryParams.append(key, params[key]);
        }
      });

      const response = await api.get(`/packages/payments/my?${queryParams.toString()}`);
      return response;
    } catch (error) {
      console.error('Get user payments error:', error);
      throw error;
    }
  },

  /**
   * 搜索付款记录
   * @param {Object} params - 搜索参数
   * @param {number} params.userId - 用户ID
   * @param {number} params.packageBookingId - 预订ID
   * @param {string} params.status - 付款状态
   * @param {string} params.paymentMethod - 付款方式
   * @param {string} params.startTime - 开始时间
   * @param {string} params.endTime - 结束时间
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页大小
   * @returns {Promise} 搜索结果
   */
  searchPayments: async (params = {}) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
          queryParams.append(key, params[key]);
        }
      });

      const response = await api.get(`/packages/payments/search?${queryParams.toString()}`);
      return response;
    } catch (error) {
      console.error('Search payments error:', error);
      throw error;
    }
  },

  /**
   * 取消付款
   * @param {string} paymentNumber - 付款编号
   * @param {string} reason - 取消原因
   * @returns {Promise} 取消结果
   */
  cancelPayment: async (paymentNumber, reason = '用户取消') => {
    try {
      const response = await api.post(`/packages/payments/${paymentNumber}/cancel`, null, {
        params: { reason }
      });
      return response;
    } catch (error) {
      console.error('Cancel payment error:', error);
      throw error;
    }
  },

  /**
   * 申请退款
   * @param {string} paymentNumber - 付款编号
   * @param {number} refundAmount - 退款金额
   * @param {string} reason - 退款原因
   * @returns {Promise} 退款结果
   */
  requestRefund: async (paymentNumber, refundAmount, reason) => {
    try {
      const response = await api.post(`/packages/payments/${paymentNumber}/refund`, null, {
        params: { refundAmount, reason }
      });
      return response;
    } catch (error) {
      console.error('Request refund error:', error);
      throw error;
    }
  },

  /**
   * 获取付款统计信息
   * @returns {Promise} 统计信息
   */
  getPaymentStatistics: async () => {
    try {
      const response = await api.get('/packages/payments/statistics');
      return response;
    } catch (error) {
      console.error('Get payment statistics error:', error);
      throw error;
    }
  },

  /**
   * 轮询付款状态
   * @param {string} paymentNumber - 付款编号
   * @param {number} maxAttempts - 最大尝试次数
   * @param {number} interval - 轮询间隔（毫秒）
   * @returns {Promise} 最终付款状态
   */
  pollPaymentStatus: async (paymentNumber, maxAttempts = 30, interval = 2000) => {
    let attempts = 0;
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          const response = await packagePaymentService.getPayment(paymentNumber);
          
          if (response.success && response.data) {
            const payment = response.data;
            
            // 如果付款成功或失败，停止轮询
            if (payment.status === 'SUCCESS' || payment.status === 'FAILED' || 
                payment.status === 'CANCELLED' || payment.expired) {
              resolve(response);
              return;
            }
          }
          
          // 如果达到最大尝试次数，停止轮询
          if (attempts >= maxAttempts) {
            reject(new Error('付款状态查询超时'));
            return;
          }
          
          // 继续轮询
          setTimeout(poll, interval);
          
        } catch (error) {
          reject(error);
        }
      };
      
      poll();
    });
  },

  /**
   * 验证付款参数
   * @param {Object} paymentData - 付款数据
   * @returns {Object} 验证结果
   */
  validatePaymentData: (paymentData) => {
    const errors = {};
    
    if (!paymentData.amount || paymentData.amount <= 0) {
      errors.amount = '付款金额必须大于0';
    }
    
    if (!paymentData.paymentMethod) {
      errors.paymentMethod = '请选择付款方式';
    }
    
    // 银行卡付款的额外验证
    if (paymentData.paymentMethod === 'CREDIT_CARD' || paymentData.paymentMethod === 'BANK_CARD') {
      if (!paymentData.cardNumber) {
        errors.cardNumber = '请输入银行卡号';
      } else if (!/^\d{16,19}$/.test(paymentData.cardNumber.replace(/\s/g, ''))) {
        errors.cardNumber = '银行卡号格式不正确';
      }
      
      if (!paymentData.cardHolderName) {
        errors.cardHolderName = '请输入持卡人姓名';
      }
      
      if (!paymentData.expiryMonth || !paymentData.expiryYear) {
        errors.expiry = '请输入卡片有效期';
      }
      
      if (!paymentData.cvv) {
        errors.cvv = '请输入CVV安全码';
      } else if (!/^\d{3,4}$/.test(paymentData.cvv)) {
        errors.cvv = 'CVV格式不正确';
      }
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * 格式化付款金额
   * @param {number} amount - 金额
   * @param {string} currency - 货币类型
   * @returns {string} 格式化后的金额
   */
  formatAmount: (amount, currency = 'CNY') => {
    const formatter = new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
    
    return formatter.format(amount);
  },

  /**
   * 获取付款方式显示名称
   * @param {string} paymentMethod - 付款方式
   * @returns {string} 显示名称
   */
  getPaymentMethodName: (paymentMethod) => {
    const methodNames = {
      'ALIPAY': '支付宝',
      'WECHAT_PAY': '微信支付',
      'CREDIT_CARD': '信用卡',
      'BANK_CARD': '银行卡',
      'CASH': '现金',
      'BANK_TRANSFER': '银行转账'
    };
    
    return methodNames[paymentMethod] || paymentMethod;
  },

  /**
   * 获取付款状态显示名称
   * @param {string} status - 付款状态
   * @returns {string} 显示名称
   */
  getPaymentStatusName: (status) => {
    const statusNames = {
      'PENDING': '待付款',
      'PROCESSING': '处理中',
      'SUCCESS': '付款成功',
      'FAILED': '付款失败',
      'CANCELLED': '已取消',
      'REFUNDED': '已退款',
      'PARTIAL_REFUND': '部分退款'
    };
    
    return statusNames[status] || status;
  },

  /**
   * 获取付款状态颜色
   * @param {string} status - 付款状态
   * @returns {string} 颜色值
   */
  getPaymentStatusColor: (status) => {
    const statusColors = {
      'PENDING': 'orange',
      'PROCESSING': 'blue',
      'SUCCESS': 'green',
      'FAILED': 'red',
      'CANCELLED': 'gray',
      'REFUNDED': 'purple',
      'PARTIAL_REFUND': 'purple'
    };

    return statusColors[status] || 'default';
  },

  /**
   * 获取支付详情
   * @param {string} paymentNumber - 支付单号
   * @returns {Promise} 支付详情
   */
  getPaymentDetails: async (paymentNumber) => {
    try {
      const response = await api.get(`/packages/payments/${paymentNumber}`);
      return response;
    } catch (error) {
      console.error('Get payment details error:', error);
      throw error;
    }
  },

  /**
   * 模拟支付成功
   * @param {string} paymentNumber - 支付单号
   * @returns {Promise} 支付结果
   */
  simulatePaymentSuccess: async (paymentNumber) => {
    try {
      const response = await api.post(`/packages/payments/${paymentNumber}/simulate/success`);
      return response;
    } catch (error) {
      console.error('Simulate payment success error:', error);
      throw error;
    }
  },

  /**
   * 模拟支付失败
   * @param {string} paymentNumber - 支付单号
   * @param {string} reason - 失败原因
   * @returns {Promise} 支付结果
   */
  simulatePaymentFailure: async (paymentNumber, reason = '模拟支付失败') => {
    try {
      const response = await api.post(`/packages/payments/${paymentNumber}/simulate/failure`, null, {
        params: { reason }
      });
      return response;
    } catch (error) {
      console.error('Simulate payment failure error:', error);
      throw error;
    }
  }
};

export default packagePaymentService;
