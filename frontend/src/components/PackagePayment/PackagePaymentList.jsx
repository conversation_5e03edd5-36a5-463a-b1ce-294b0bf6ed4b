import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  InputNumber,
  message,
  Tooltip,
  Popconfirm
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  StopOutlined,
  UndoOutlined,
  ExportOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { packagePaymentService } from '../../services/packagePaymentService';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

/**
 * 套餐付款记录列表组件
 */
const PackagePaymentList = ({ 
  userId, 
  packageBookingId, 
  showUserInfo = true,
  showBookingInfo = true 
}) => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [payments, setPayments] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({});
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [refundModalVisible, setRefundModalVisible] = useState(false);
  const [refundForm] = Form.useForm();

  // 付款状态选项
  const statusOptions = [
    { value: 'PENDING', label: '待付款', color: 'orange' },
    { value: 'PROCESSING', label: '处理中', color: 'blue' },
    { value: 'SUCCESS', label: '付款成功', color: 'green' },
    { value: 'FAILED', label: '付款失败', color: 'red' },
    { value: 'CANCELLED', label: '已取消', color: 'gray' },
    { value: 'REFUNDED', label: '已退款', color: 'purple' },
    { value: 'PARTIAL_REFUND', label: '部分退款', color: 'purple' }
  ];

  // 付款方式选项
  const methodOptions = [
    { value: 'ALIPAY', label: '支付宝' },
    { value: 'WECHAT_PAY', label: '微信支付' },
    { value: 'CREDIT_CARD', label: '信用卡' },
    { value: 'BANK_CARD', label: '银行卡' },
    { value: 'CASH', label: '现金' },
    { value: 'BANK_TRANSFER', label: '银行转账' }
  ];

  // 初始化加载数据
  useEffect(() => {
    loadPayments();
  }, [userId, packageBookingId, pagination.current, pagination.pageSize, searchParams]);

  // 加载付款记录
  const loadPayments = async () => {
    setLoading(true);
    
    try {
      let response;
      
      if (packageBookingId) {
        // 获取特定预订的付款记录
        response = await packagePaymentService.getBookingPayments(packageBookingId);
        setPayments(response.success ? response.data : []);
        setPagination(prev => ({ ...prev, total: response.data?.length || 0 }));
      } else if (userId) {
        // 获取用户的付款记录
        response = await packagePaymentService.getUserPayments({
          page: pagination.current - 1,
          size: pagination.pageSize,
          ...searchParams
        });
        
        if (response.success) {
          setPayments(response.data.content);
          setPagination(prev => ({
            ...prev,
            total: response.data.totalElements
          }));
        }
      } else {
        // 搜索所有付款记录
        response = await packagePaymentService.searchPayments({
          page: pagination.current - 1,
          size: pagination.pageSize,
          ...searchParams
        });
        
        if (response.success) {
          setPayments(response.data.content);
          setPagination(prev => ({
            ...prev,
            total: response.data.totalElements
          }));
        }
      }
    } catch (error) {
      console.error('Load payments failed:', error);
      message.error('加载付款记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = (values) => {
    const params = { ...values };
    
    // 处理时间范围
    if (values.dateRange && values.dateRange.length === 2) {
      params.startTime = values.dateRange[0].format('YYYY-MM-DD HH:mm:ss');
      params.endTime = values.dateRange[1].format('YYYY-MM-DD HH:mm:ss');
      delete params.dateRange;
    }
    
    setSearchParams(params);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 重置搜索
  const handleReset = () => {
    setSearchParams({});
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 查看付款详情
  const handleViewDetail = (payment) => {
    setSelectedPayment(payment);
    setDetailModalVisible(true);
  };

  // 取消付款
  const handleCancelPayment = async (paymentNumber) => {
    try {
      const response = await packagePaymentService.cancelPayment(paymentNumber, '用户取消');
      
      if (response.success) {
        message.success('付款取消成功');
        loadPayments();
      } else {
        message.error(response.message || '取消付款失败');
      }
    } catch (error) {
      console.error('Cancel payment failed:', error);
      message.error('取消付款失败');
    }
  };

  // 申请退款
  const handleRefundRequest = (payment) => {
    setSelectedPayment(payment);
    setRefundModalVisible(true);
    refundForm.setFieldsValue({
      refundAmount: payment.amount - (payment.refundAmount || 0)
    });
  };

  // 提交退款申请
  const handleRefundSubmit = async (values) => {
    if (!selectedPayment) return;
    
    try {
      const response = await packagePaymentService.requestRefund(
        selectedPayment.paymentNumber,
        values.refundAmount,
        values.reason
      );
      
      if (response.success) {
        message.success('退款申请提交成功');
        setRefundModalVisible(false);
        refundForm.resetFields();
        loadPayments();
      } else {
        message.error(response.message || '退款申请失败');
      }
    } catch (error) {
      console.error('Refund request failed:', error);
      message.error('退款申请失败');
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '付款编号',
      dataIndex: 'paymentNumber',
      key: 'paymentNumber',
      width: 180,
      render: (text) => (
        <Text copyable={{ text }} style={{ fontFamily: 'monospace' }}>
          {text}
        </Text>
      )
    },
    ...(showBookingInfo ? [{
      title: '套餐名称',
      dataIndex: 'packageName',
      key: 'packageName',
      ellipsis: true
    }] : []),
    ...(showUserInfo ? [{
      title: '用户',
      dataIndex: 'userName',
      key: 'userName',
      width: 120
    }] : []),
    {
      title: '付款金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount) => (
        <Text strong style={{ color: '#cf1322' }}>
          ¥{amount?.toFixed(2)}
        </Text>
      )
    },
    {
      title: '付款方式',
      dataIndex: 'paymentMethod',
      key: 'paymentMethod',
      width: 100,
      render: (method) => packagePaymentService.getPaymentMethodName(method)
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={packagePaymentService.getPaymentStatusColor(status)}>
          {packagePaymentService.getPaymentStatusName(status)}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          
          {record.status === 'PENDING' && (
            <Popconfirm
              title="确定要取消这笔付款吗？"
              onConfirm={() => handleCancelPayment(record.paymentNumber)}
            >
              <Tooltip title="取消付款">
                <Button
                  type="text"
                  icon={<StopOutlined />}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          )}
          
          {record.status === 'SUCCESS' && record.amount > (record.refundAmount || 0) && (
            <Tooltip title="申请退款">
              <Button
                type="text"
                icon={<UndoOutlined />}
                onClick={() => handleRefundRequest(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  // 渲染搜索表单
  const renderSearchForm = () => (
    <Card className="mb-4">
      <Form
        layout="inline"
        onFinish={handleSearch}
        className="search-form"
      >
        <Form.Item name="paymentNumber">
          <Input placeholder="付款编号" style={{ width: 180 }} />
        </Form.Item>
        
        <Form.Item name="status">
          <Select placeholder="付款状态" style={{ width: 120 }} allowClear>
            {statusOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item name="paymentMethod">
          <Select placeholder="付款方式" style={{ width: 120 }} allowClear>
            {methodOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item name="dateRange">
          <RangePicker
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            placeholder={['开始时间', '结束时间']}
          />
        </Form.Item>
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              搜索
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
            <Button icon={<ReloadOutlined />} onClick={loadPayments}>
              刷新
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );

  return (
    <div>
      {/* 搜索表单 */}
      {!packageBookingId && renderSearchForm()}
      
      {/* 付款记录表格 */}
      <Card
        title={
          <Space>
            <Title level={4} style={{ margin: 0 }}>
              付款记录
            </Title>
            <Tag color="blue">{pagination.total} 条记录</Tag>
          </Space>
        }
        extra={
          <Space>
            <Button icon={<ExportOutlined />}>
              导出
            </Button>
            <Button icon={<ReloadOutlined />} onClick={loadPayments}>
              刷新
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={payments}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize
              }));
            }
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 付款详情模态框 */}
      <Modal
        title="付款详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedPayment && (
          <div>
            <Row gutter={[16, 16]}>
              <Col span={8}>
                <Statistic title="付款编号" value={selectedPayment.paymentNumber} />
              </Col>
              <Col span={8}>
                <Statistic 
                  title="付款金额" 
                  value={selectedPayment.amount} 
                  precision={2}
                  prefix="¥"
                />
              </Col>
              <Col span={8}>
                <Statistic 
                  title="付款状态" 
                  value={packagePaymentService.getPaymentStatusName(selectedPayment.status)}
                />
              </Col>
              <Col span={8}>
                <Statistic 
                  title="付款方式" 
                  value={packagePaymentService.getPaymentMethodName(selectedPayment.paymentMethod)}
                />
              </Col>
              <Col span={8}>
                <Statistic title="创建时间" value={dayjs(selectedPayment.createdAt).format('YYYY-MM-DD HH:mm:ss')} />
              </Col>
              {selectedPayment.processedAt && (
                <Col span={8}>
                  <Statistic title="处理时间" value={dayjs(selectedPayment.processedAt).format('YYYY-MM-DD HH:mm:ss')} />
                </Col>
              )}
            </Row>
            
            {selectedPayment.refundAmount > 0 && (
              <Row gutter={[16, 16]} className="mt-4">
                <Col span={8}>
                  <Statistic 
                    title="退款金额" 
                    value={selectedPayment.refundAmount} 
                    precision={2}
                    prefix="¥"
                  />
                </Col>
                {selectedPayment.refundAt && (
                  <Col span={8}>
                    <Statistic title="退款时间" value={dayjs(selectedPayment.refundAt).format('YYYY-MM-DD HH:mm:ss')} />
                  </Col>
                )}
              </Row>
            )}
          </div>
        )}
      </Modal>

      {/* 退款申请模态框 */}
      <Modal
        title="申请退款"
        open={refundModalVisible}
        onCancel={() => setRefundModalVisible(false)}
        footer={null}
      >
        <Form
          form={refundForm}
          layout="vertical"
          onFinish={handleRefundSubmit}
        >
          <Form.Item
            name="refundAmount"
            label="退款金额"
            rules={[
              { required: true, message: '请输入退款金额' },
              { type: 'number', min: 0.01, message: '退款金额必须大于0' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              precision={2}
              min={0.01}
              max={selectedPayment ? selectedPayment.amount - (selectedPayment.refundAmount || 0) : 0}
              prefix="¥"
            />
          </Form.Item>
          
          <Form.Item
            name="reason"
            label="退款原因"
            rules={[{ required: true, message: '请输入退款原因' }]}
          >
            <Input.TextArea rows={4} placeholder="请说明退款原因" />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交申请
              </Button>
              <Button onClick={() => setRefundModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default PackagePaymentList;
