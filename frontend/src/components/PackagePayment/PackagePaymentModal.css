/* 文化套餐付款模态框样式优化 */

/* 模态框整体样式 */
.package-payment-modal {
  border-radius: 12px;
  overflow: hidden;
}

.package-payment-modal .ant-modal-content {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.package-payment-modal .ant-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  padding: 20px 24px;
}

.package-payment-modal .ant-modal-title {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.package-payment-modal .ant-modal-close {
  color: white;
  opacity: 0.8;
}

.package-payment-modal .ant-modal-close:hover {
  opacity: 1;
}

.package-payment-modal .ant-modal-body {
  padding: 24px;
  background: #fafafa;
}

/* 步骤条样式优化 */
.payment-steps {
  margin-bottom: 32px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.payment-steps .ant-steps-item-title {
  font-weight: 500;
  font-size: 14px;
}

.payment-steps .ant-steps-item-description {
  color: #8c8c8c;
  font-size: 12px;
}

/* 付款方式选择区域 */
.payment-method-section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.payment-method-section .ant-card-head {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.payment-method-section .ant-card-head-title {
  font-weight: 600;
  color: #495057;
}

/* 付款方式选项样式 */
.payment-method-options {
  padding: 8px 0;
}

.payment-method-option {
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  background: white;
  overflow: hidden;
}

.payment-method-option:last-child {
  margin-bottom: 0;
}

.payment-method-option:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.payment-method-option.ant-radio-button-wrapper-checked {
  border-color: #1890ff;
  background: #f6ffed;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.payment-method-option .ant-radio-button {
  border: none;
  background: transparent;
  padding: 16px 20px;
  height: auto;
  line-height: 1.5;
}

.payment-method-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.payment-method-icon {
  font-size: 24px;
  margin-right: 16px;
  flex-shrink: 0;
}

.payment-method-info {
  flex: 1;
}

.payment-method-label {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.payment-method-description {
  font-size: 13px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 银行卡信息表单 */
.bank-card-form {
  margin-top: 20px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.bank-card-form .ant-card-head {
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  padding: 12px 20px;
}

.bank-card-form .ant-card-head-title {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.bank-card-form .ant-card-body {
  padding: 20px;
  background: white;
}

.bank-card-form .ant-form-item {
  margin-bottom: 16px;
}

.bank-card-form .ant-form-item-label > label {
  font-weight: 500;
  color: #495057;
}

.bank-card-form .ant-input {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
}

.bank-card-form .ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 付款信息卡片 */
.payment-info-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.payment-info-section .ant-card-head {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  border-bottom: none;
}

.payment-info-section .ant-card-head-title {
  color: white;
  font-weight: 600;
}

.payment-info-section .ant-card-body {
  padding: 24px;
}

.payment-info-row {
  margin-bottom: 0;
}

.payment-info-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.payment-info-item:last-child {
  margin-bottom: 0;
}

.payment-info-item .ant-statistic-title {
  font-size: 13px;
  color: #8c8c8c;
  margin-bottom: 8px;
  font-weight: 500;
}

.payment-info-item .ant-statistic-content {
  font-size: 18px;
  font-weight: 600;
}

.payment-amount .ant-statistic-content {
  color: #cf1322;
  font-size: 24px;
  font-weight: 700;
}

/* 操作按钮区域 */
.payment-actions {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.payment-actions .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 24px;
  height: auto;
}

.payment-actions .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.payment-actions .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
  transform: translateY(-1px);
}

/* 付款确认步骤样式 */
.payment-confirm-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.payment-confirm-content {
  padding: 32px;
  text-align: center;
}

.payment-countdown {
  margin-bottom: 24px;
}

.payment-countdown .ant-alert {
  border-radius: 8px;
  border: none;
  background: linear-gradient(135deg, #fff7e6 0%, #fff1b8 100%);
}

.payment-details {
  margin: 24px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.payment-details .ant-typography {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 600;
}

.payment-details-row {
  margin-bottom: 16px;
}

.payment-details-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.payment-qr-section {
  margin: 24px 0;
  padding: 24px;
  background: white;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
}

.payment-qr-section .ant-typography {
  margin-bottom: 16px;
  color: #262626;
  font-weight: 500;
}

.payment-status-tag {
  margin: 16px 0;
}

.payment-confirm-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .package-payment-modal {
    margin: 0;
    max-width: 100vw;
    width: 100vw !important;
  }

  .package-payment-modal .ant-modal-content {
    border-radius: 0;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .package-payment-modal .ant-modal-header {
    flex-shrink: 0;
    padding: 16px 20px;
  }

  .package-payment-modal .ant-modal-body {
    padding: 16px;
    flex: 1;
    overflow-y: auto;
    background: #f8f9fa;
  }

  .payment-steps {
    padding: 16px;
    margin-bottom: 20px;
  }

  .payment-steps .ant-steps-item-title {
    font-size: 12px;
  }

  .payment-steps .ant-steps-item-description {
    display: none;
  }

  .payment-method-section {
    margin-bottom: 16px;
  }

  .payment-method-option .ant-radio-button {
    padding: 12px 16px;
  }

  .payment-method-icon {
    font-size: 20px;
    margin-right: 12px;
  }

  .payment-method-label {
    font-size: 14px;
  }

  .payment-method-description {
    font-size: 12px;
  }

  .payment-info-section {
    margin-bottom: 16px;
  }

  .payment-info-item {
    padding: 12px;
    margin-bottom: 12px;
  }

  .payment-info-item .ant-statistic-content {
    font-size: 16px;
  }

  .payment-amount .ant-statistic-content {
    font-size: 20px;
  }

  .payment-actions {
    flex-direction: column;
    gap: 8px;
    margin-top: 20px;
    padding-top: 16px;
    position: sticky;
    bottom: 0;
    background: #fafafa;
    border-top: 1px solid #f0f0f0;
  }

  .payment-actions .ant-btn {
    width: 100%;
    height: 44px;
    font-size: 16px;
  }

  .payment-confirm-content {
    padding: 20px;
  }

  .payment-confirm-actions {
    flex-direction: column;
    gap: 8px;
  }

  .payment-confirm-actions .ant-btn {
    width: 100%;
    height: 44px;
    font-size: 16px;
  }

  .bank-card-form .ant-card-body {
    padding: 16px;
  }

  .payment-details {
    padding: 16px;
    margin: 20px 0;
  }

  .payment-qr-section {
    padding: 20px;
    margin: 20px 0;
  }
}

@media (max-width: 480px) {
  .package-payment-modal .ant-modal-header {
    padding: 12px 16px;
  }

  .package-payment-modal .ant-modal-title {
    font-size: 16px;
  }

  .package-payment-modal .ant-modal-body {
    padding: 12px;
  }

  .payment-steps {
    padding: 12px;
    margin-bottom: 16px;
  }

  .payment-method-section .ant-card-head {
    padding: 12px 16px;
  }

  .payment-method-section .ant-card-body {
    padding: 16px;
  }

  .payment-method-option .ant-radio-button {
    padding: 10px 12px;
  }

  .payment-method-icon {
    font-size: 18px;
    margin-right: 10px;
  }

  .payment-method-label {
    font-size: 13px;
  }

  .payment-method-description {
    font-size: 11px;
  }

  .payment-info-row .ant-col {
    margin-bottom: 12px;
  }

  .payment-info-item {
    padding: 10px;
  }

  .payment-info-item .ant-statistic-title {
    font-size: 12px;
  }

  .payment-info-item .ant-statistic-content {
    font-size: 14px;
  }

  .payment-amount .ant-statistic-content {
    font-size: 18px;
  }

  .payment-details-row .ant-col {
    margin-bottom: 12px;
  }

  .payment-details-item {
    padding: 10px;
  }

  .payment-details-item .ant-statistic-title {
    font-size: 11px;
  }

  .payment-details-item .ant-statistic-content {
    font-size: 12px;
  }

  .bank-card-form .ant-card-body {
    padding: 12px;
  }

  .bank-card-form .ant-form-item {
    margin-bottom: 12px;
  }

  .payment-actions {
    padding: 12px 0;
    margin-top: 16px;
  }

  .payment-actions .ant-btn {
    height: 40px;
    font-size: 14px;
  }

  .payment-confirm-content {
    padding: 16px;
  }

  .payment-confirm-actions .ant-btn {
    height: 40px;
    font-size: 14px;
  }

  .payment-qr-section {
    padding: 16px;
    margin: 16px 0;
  }

  .payment-details {
    padding: 12px;
    margin: 16px 0;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .payment-method-content {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }

  .payment-method-icon {
    margin-right: 0;
    margin-bottom: 8px;
    align-self: center;
  }

  .payment-method-info {
    text-align: center;
    width: 100%;
  }

  .payment-info-item .ant-statistic-content {
    font-size: 13px;
  }

  .payment-amount .ant-statistic-content {
    font-size: 16px;
  }
}
