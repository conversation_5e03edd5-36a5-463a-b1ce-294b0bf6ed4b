import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Typography,
  Space,
  Button,
  DatePicker,
  Spin,
  Empty,
  Tag
} from 'antd';
import {
  DollarOutlined,
  TransactionOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
  TrendingUpOutlined
} from '@ant-design/icons';
import { packagePaymentService } from '../../services/packagePaymentService';
import { Column, Pie } from '@ant-design/plots';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

/**
 * 套餐付款统计组件
 */
const PackagePaymentStats = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState(null);
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(30, 'day'),
    dayjs()
  ]);

  // 初始化加载数据
  useEffect(() => {
    loadStatistics();
  }, []);

  // 加载统计数据
  const loadStatistics = async () => {
    setLoading(true);
    
    try {
      const response = await packagePaymentService.getPaymentStatistics();
      
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('Load statistics failed:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
    // 这里可以根据日期范围重新加载数据
  };

  // 渲染概览统计卡片
  const renderOverviewCards = () => {
    if (!statistics) return null;

    return (
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日付款笔数"
              value={statistics.todayPaymentCount || 0}
              prefix={<TransactionOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日收入"
              value={statistics.todayRevenue || 0}
              precision={2}
              prefix={<DollarOutlined />}
              suffix="元"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="成功率"
              value={calculateSuccessRate()}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="失败笔数"
              value={getFailedCount()}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 计算成功率
  const calculateSuccessRate = () => {
    if (!statistics?.statusStatistics) return 0;
    
    const total = Object.values(statistics.statusStatistics).reduce((sum, count) => sum + count, 0);
    const success = statistics.statusStatistics.SUCCESS || 0;
    
    return total > 0 ? ((success / total) * 100).toFixed(1) : 0;
  };

  // 获取失败笔数
  const getFailedCount = () => {
    if (!statistics?.statusStatistics) return 0;
    return statistics.statusStatistics.FAILED || 0;
  };

  // 渲染状态分布图表
  const renderStatusChart = () => {
    if (!statistics?.statusStatistics) return <Empty description="暂无数据" />;

    const data = Object.entries(statistics.statusStatistics).map(([status, count]) => ({
      status: packagePaymentService.getPaymentStatusName(status),
      count,
      percentage: ((count / Object.values(statistics.statusStatistics).reduce((sum, c) => sum + c, 0)) * 100).toFixed(1)
    }));

    const config = {
      data,
      angleField: 'count',
      colorField: 'status',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name} {percentage}%'
      },
      interactions: [
        {
          type: 'element-active'
        }
      ]
    };

    return <Pie {...config} />;
  };

  // 渲染付款方式分布图表
  const renderPaymentMethodChart = () => {
    if (!statistics?.paymentMethodStatistics) return <Empty description="暂无数据" />;

    const data = Object.entries(statistics.paymentMethodStatistics).map(([method, amount]) => ({
      method: packagePaymentService.getPaymentMethodName(method),
      amount: parseFloat(amount)
    }));

    const config = {
      data,
      xField: 'method',
      yField: 'amount',
      label: {
        position: 'middle',
        style: {
          fill: '#FFFFFF',
          opacity: 0.6
        }
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: false
        }
      },
      meta: {
        method: {
          alias: '付款方式'
        },
        amount: {
          alias: '金额(元)'
        }
      }
    };

    return <Column {...config} />;
  };

  // 渲染状态统计表格
  const renderStatusTable = () => {
    if (!statistics?.statusStatistics) return <Empty description="暂无数据" />;

    const data = Object.entries(statistics.statusStatistics).map(([status, count]) => ({
      key: status,
      status,
      statusName: packagePaymentService.getPaymentStatusName(status),
      count,
      percentage: ((count / Object.values(statistics.statusStatistics).reduce((sum, c) => sum + c, 0)) * 100).toFixed(1)
    }));

    const columns = [
      {
        title: '状态',
        dataIndex: 'statusName',
        key: 'statusName',
        render: (text, record) => (
          <Tag color={packagePaymentService.getPaymentStatusColor(record.status)}>
            {text}
          </Tag>
        )
      },
      {
        title: '数量',
        dataIndex: 'count',
        key: 'count',
        align: 'right'
      },
      {
        title: '占比',
        dataIndex: 'percentage',
        key: 'percentage',
        align: 'right',
        render: (percentage) => (
          <Space>
            <Text>{percentage}%</Text>
            <Progress 
              percent={parseFloat(percentage)} 
              size="small" 
              showInfo={false}
              style={{ width: 60 }}
            />
          </Space>
        )
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={data}
        pagination={false}
        size="small"
      />
    );
  };

  // 渲染付款方式统计表格
  const renderPaymentMethodTable = () => {
    if (!statistics?.paymentMethodStatistics) return <Empty description="暂无数据" />;

    const totalAmount = Object.values(statistics.paymentMethodStatistics)
      .reduce((sum, amount) => sum + parseFloat(amount), 0);

    const data = Object.entries(statistics.paymentMethodStatistics).map(([method, amount]) => ({
      key: method,
      method,
      methodName: packagePaymentService.getPaymentMethodName(method),
      amount: parseFloat(amount),
      percentage: ((parseFloat(amount) / totalAmount) * 100).toFixed(1)
    }));

    const columns = [
      {
        title: '付款方式',
        dataIndex: 'methodName',
        key: 'methodName'
      },
      {
        title: '金额',
        dataIndex: 'amount',
        key: 'amount',
        align: 'right',
        render: (amount) => `¥${amount.toFixed(2)}`
      },
      {
        title: '占比',
        dataIndex: 'percentage',
        key: 'percentage',
        align: 'right',
        render: (percentage) => (
          <Space>
            <Text>{percentage}%</Text>
            <Progress 
              percent={parseFloat(percentage)} 
              size="small" 
              showInfo={false}
              style={{ width: 60 }}
            />
          </Space>
        )
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={data}
        pagination={false}
        size="small"
      />
    );
  };

  return (
    <div>
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center mb-6">
        <Title level={3}>
          <TrendingUpOutlined className="mr-2" />
          付款统计
        </Title>
        
        <Space>
          <RangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            format="YYYY-MM-DD"
          />
          <Button 
            icon={<ReloadOutlined />} 
            onClick={loadStatistics}
            loading={loading}
          >
            刷新
          </Button>
        </Space>
      </div>

      <Spin spinning={loading}>
        {/* 概览统计卡片 */}
        <div className="mb-6">
          {renderOverviewCards()}
        </div>

        {/* 图表和表格 */}
        <Row gutter={[16, 16]}>
          {/* 状态分布 */}
          <Col xs={24} lg={12}>
            <Card title="付款状态分布" className="h-full">
              <div style={{ height: 300 }}>
                {renderStatusChart()}
              </div>
            </Card>
          </Col>

          {/* 付款方式分布 */}
          <Col xs={24} lg={12}>
            <Card title="付款方式收入分布" className="h-full">
              <div style={{ height: 300 }}>
                {renderPaymentMethodChart()}
              </div>
            </Card>
          </Col>

          {/* 状态统计表格 */}
          <Col xs={24} lg={12}>
            <Card title="状态统计详情" className="h-full">
              {renderStatusTable()}
            </Card>
          </Col>

          {/* 付款方式统计表格 */}
          <Col xs={24} lg={12}>
            <Card title="付款方式统计详情" className="h-full">
              {renderPaymentMethodTable()}
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default PackagePaymentStats;
