import React from 'react';
import { Layout, Menu, Button, Space, Dropdown, Avatar, message } from 'antd';
import { HomeOutlined, SearchOutlined, UserOutlined, LoginOutlined, LogoutOutlined, SettingOutlined, CalendarOutlined, StarOutlined, MessageOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const { Header: AntHeader } = Layout;

const Header = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user, logout } = useAuth();

  const menuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '首页',
      onClick: () => navigate('/')
    },
    {
      key: 'search',
      icon: <SearchOutlined />,
      label: '酒店搜索',
      onClick: () => navigate('/search')
    },
    {
      key: 'packages',
      icon: <StarOutlined />,
      label: '文化套餐',
      onClick: () => navigate('/packages')
    }
  ];

  // 处理登出
  const handleLogout = () => {
    logout();
    message.success('已成功登出');
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
      onClick: () => navigate('/profile')
    },
    {
      key: 'bookings',
      icon: <CalendarOutlined />,
      label: '我的预订',
      onClick: () => navigate('/profile/bookings')
    },
    {
      key: 'package-bookings',
      icon: <CalendarOutlined />,
      label: '套餐预订',
      onClick: () => navigate('/my-package-bookings')
    },
    {
      key: 'reviews',
      icon: <MessageOutlined />,
      label: '我的评价',
      onClick: () => navigate('/my-reviews')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
      onClick: () => navigate('/profile')
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout
    }
  ];

  return (
    <AntHeader style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'space-between',
      background: '#fff',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ 
          fontSize: '20px', 
          fontWeight: 'bold', 
          color: '#1890ff',
          marginRight: '40px'
        }}>
          甘孜州酒店预订系统
        </div>
        <Menu
          mode="horizontal"
          items={menuItems}
          style={{ border: 'none', flex: 1 }}
        />
      </div>
      <Space>
        {isAuthenticated ? (
          // 已登录状态
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            arrow
          >
            <Space style={{ cursor: 'pointer' }}>
              <Avatar
                size="small"
                icon={<UserOutlined />}
                src={user?.avatarUrl}
              />
              <span style={{ color: '#262626' }}>
                {user?.fullName || user?.username}
              </span>
            </Space>
          </Dropdown>
        ) : (
          // 未登录状态
          <>
            <Button
              type="primary"
              icon={<LoginOutlined />}
              onClick={() => navigate('/login')}
            >
              登录
            </Button>
            <Button
              icon={<UserOutlined />}
              onClick={() => navigate('/register')}
            >
              注册
            </Button>
          </>
        )}
      </Space>
    </AntHeader>
  );
};

export default Header;
