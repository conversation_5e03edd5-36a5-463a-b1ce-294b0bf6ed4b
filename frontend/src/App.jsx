import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import './App.css';
import { AuthProvider } from './context/AuthContext';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Profile from './pages/Profile';
import HotelSearch from './pages/HotelSearch';
import HotelDetail from './pages/HotelDetail';
import Booking from './pages/Booking';
import MyBookings from './pages/MyBookings';
import MyReviews from './pages/MyReviews';
import CulturalPackages from './pages/CulturalPackages';
import CulturalPackageDetail from './pages/CulturalPackageDetail';
import MyPackageBookings from './pages/MyPackageBookings';
import TestAPI from './pages/TestAPI';
import TestPackagePayment from './pages/TestPackagePayment';
import DebugPackagePayment from './pages/DebugPackagePayment';
import Header from './components/common/Header';
import Footer from './components/common/Footer';
import PrivateRoute from './components/common/PrivateRoute';

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <AuthProvider>
        <Router>
          <div className="App">
            <Header />
            <main className="main-content">
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/search" element={<HotelSearch />} />
                <Route path="/test-api" element={<TestAPI />} />
                <Route path="/test-payment" element={<TestPackagePayment />} />
                <Route path="/debug-payment" element={<DebugPackagePayment />} />
                <Route path="/hotels/:id" element={<HotelDetail />} />
                <Route path="/packages" element={<CulturalPackages />} />
                <Route path="/packages/:id" element={<CulturalPackageDetail />} />
                <Route
                  path="/booking"
                  element={
                    <PrivateRoute>
                      <Booking />
                    </PrivateRoute>
                  }
                />
                <Route
                  path="/profile"
                  element={
                    <PrivateRoute>
                      <Profile />
                    </PrivateRoute>
                  }
                />
                <Route
                  path="/profile/bookings"
                  element={
                    <PrivateRoute>
                      <MyBookings />
                    </PrivateRoute>
                  }
                />
                <Route
                  path="/my-reviews"
                  element={
                    <PrivateRoute>
                      <MyReviews />
                    </PrivateRoute>
                  }
                />
                <Route
                  path="/my-package-bookings"
                  element={
                    <PrivateRoute>
                      <MyPackageBookings />
                    </PrivateRoute>
                  }
                />
              </Routes>
            </main>
            <Footer />
          </div>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
