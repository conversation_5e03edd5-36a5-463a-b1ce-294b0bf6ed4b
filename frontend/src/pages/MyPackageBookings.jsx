import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Tag,
  Space,
  Typography,
  Tabs,
  Empty,
  Spin,
  Modal,
  Descriptions,
  Row,
  Col,
  Statistic,
  message,
  Popconfirm,
  Divider
} from 'antd';
import {
  EyeOutlined,
  PaymentOutlined,
  StopOutlined,
  CalendarOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import packageBookingService from '../services/packageBookingService';
import { PackagePaymentModal, PackagePaymentList } from '../components/PackagePayment';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * 我的套餐预订页面
 */
const MyPackageBookings = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [bookings, setBookings] = useState([]);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);

  // 预订状态映射
  const statusMap = {
    'PENDING': { text: '待确认', color: 'orange' },
    'CONFIRMED': { text: '已确认', color: 'blue' },
    'PAID': { text: '已付款', color: 'green' },
    'COMPLETED': { text: '已完成', color: 'green' },
    'CANCELLED': { text: '已取消', color: 'red' }
  };

  // 付款状态映射
  const paymentStatusMap = {
    'PENDING': { text: '待付款', color: 'orange' },
    'PARTIAL': { text: '部分付款', color: 'blue' },
    'PAID': { text: '已付款', color: 'green' },
    'REFUNDED': { text: '已退款', color: 'purple' }
  };

  // 初始化加载数据
  useEffect(() => {
    loadBookings();
  }, [activeTab]);

  // 加载预订列表
  const loadBookings = async () => {
    setLoading(true);
    
    try {
      const response = await packageBookingService.getMyBookings();
      
      if (response.success) {
        let filteredBookings = response.data.content || [];
        
        // 根据选中的标签过滤数据
        if (activeTab !== 'all') {
          filteredBookings = filteredBookings.filter(booking => {
            switch (activeTab) {
              case 'pending':
                return booking.status === 'PENDING';
              case 'confirmed':
                return booking.status === 'CONFIRMED';
              case 'paid':
                return booking.paymentStatus === 'PAID';
              case 'completed':
                return booking.status === 'COMPLETED';
              case 'cancelled':
                return booking.status === 'CANCELLED';
              default:
                return true;
            }
          });
        }
        
        setBookings(filteredBookings);
      } else {
        message.error(response.message || '加载预订列表失败');
      }
    } catch (error) {
      console.error('Load bookings failed:', error);
      message.error('加载预订列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看预订详情
  const handleViewDetail = (booking) => {
    setSelectedBooking(booking);
    setDetailModalVisible(true);
  };

  // 处理付款
  const handlePayment = (booking) => {
    setSelectedBooking(booking);
    setPaymentModalVisible(true);
  };

  // 取消预订
  const handleCancelBooking = async (bookingId) => {
    try {
      const response = await packageBookingService.cancelBooking(bookingId, '用户取消');
      
      if (response.success) {
        message.success('预订取消成功');
        loadBookings();
      } else {
        message.error(response.message || '取消预订失败');
      }
    } catch (error) {
      console.error('Cancel booking failed:', error);
      message.error('取消预订失败');
    }
  };

  // 付款成功回调
  const handlePaymentSuccess = (payment) => {
    message.success('付款成功！');
    setPaymentModalVisible(false);
    loadBookings();
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusInfo = statusMap[status] || { text: status, color: 'default' };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // 获取付款状态标签
  const getPaymentStatusTag = (paymentStatus) => {
    if (!paymentStatus) return null;
    const statusInfo = paymentStatusMap[paymentStatus] || { text: paymentStatus, color: 'default' };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // 渲染预订项
  const renderBookingItem = (booking) => {
    const canCancel = booking.status === 'PENDING' || booking.status === 'CONFIRMED';
    const needPayment = booking.status === 'CONFIRMED' && booking.paymentStatus !== 'PAID';
    
    return (
      <List.Item
        key={booking.id}
        actions={[
          <Button 
            type="link" 
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(booking)}
          >
            查看详情
          </Button>,
          needPayment && (
            <Button 
              type="link" 
              icon={<PaymentOutlined />}
              onClick={() => handlePayment(booking)}
            >
              立即付款
            </Button>
          ),
          canCancel && (
            <Popconfirm
              title="确定要取消这个预订吗？"
              onConfirm={() => handleCancelBooking(booking.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger icon={<StopOutlined />}>
                取消预订
              </Button>
            </Popconfirm>
          )
        ].filter(Boolean)}
      >
        <List.Item.Meta
          title={
            <div className="flex justify-between items-start">
              <div>
                <Text strong className="text-lg">{booking.packageName}</Text>
                <div className="mt-1">
                  <Space>
                    {getStatusTag(booking.status)}
                    {getPaymentStatusTag(booking.paymentStatus)}
                  </Space>
                </div>
              </div>
              <div className="text-right">
                <Text strong className="text-xl text-red-500">
                  ¥{booking.finalAmount?.toFixed(2)}
                </Text>
                {booking.paidAmount > 0 && (
                  <div className="text-sm text-gray-500">
                    已付：¥{booking.paidAmount.toFixed(2)}
                  </div>
                )}
              </div>
            </div>
          }
          description={
            <div className="mt-2">
              <Space direction="vertical" size="small" className="w-full">
                <div>
                  <Space>
                    <CalendarOutlined />
                    <Text>预订日期：{dayjs(booking.bookingDate).format('YYYY-MM-DD')}</Text>
                  </Space>
                </div>
                <div>
                  <Space>
                    <UserOutlined />
                    <Text>参与人数：{booking.participantCount}人</Text>
                  </Space>
                </div>
                <div>
                  <Space>
                    <Text type="secondary">预订号：{booking.bookingNumber}</Text>
                  </Space>
                </div>
                <div>
                  <Space>
                    <Text type="secondary">
                      创建时间：{dayjs(booking.createdAt).format('YYYY-MM-DD HH:mm')}
                    </Text>
                  </Space>
                </div>
              </Space>
            </div>
          }
        />
      </List.Item>
    );
  };

  // 渲染详情模态框
  const renderDetailModal = () => (
    <Modal
      title="预订详情"
      open={detailModalVisible}
      onCancel={() => setDetailModalVisible(false)}
      footer={null}
      width={800}
    >
      {selectedBooking && (
        <div>
          {/* 基本信息 */}
          <Card title="基本信息" className="mb-4">
            <Descriptions column={2} bordered>
              <Descriptions.Item label="预订号" span={2}>
                <Text copyable style={{ fontFamily: 'monospace' }}>
                  {selectedBooking.bookingNumber}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="套餐名称">
                {selectedBooking.packageName}
              </Descriptions.Item>
              <Descriptions.Item label="预订状态">
                {getStatusTag(selectedBooking.status)}
              </Descriptions.Item>
              <Descriptions.Item label="预订日期">
                {dayjs(selectedBooking.bookingDate).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="参与人数">
                {selectedBooking.participantCount}人
              </Descriptions.Item>
              <Descriptions.Item label="单价">
                ¥{selectedBooking.unitPrice?.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="总金额">
                ¥{selectedBooking.totalAmount?.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="优惠金额">
                ¥{selectedBooking.discountAmount?.toFixed(2) || '0.00'}
              </Descriptions.Item>
              <Descriptions.Item label="实付金额">
                <Text strong style={{ color: '#cf1322', fontSize: '16px' }}>
                  ¥{selectedBooking.finalAmount?.toFixed(2)}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 联系信息 */}
          <Card title="联系信息" className="mb-4">
            <Descriptions column={1} bordered>
              <Descriptions.Item label="联系人">
                <Space>
                  <UserOutlined />
                  {selectedBooking.contactName}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="联系电话">
                <Space>
                  <PhoneOutlined />
                  {selectedBooking.contactPhone}
                </Space>
              </Descriptions.Item>
              {selectedBooking.contactEmail && (
                <Descriptions.Item label="邮箱">
                  <Space>
                    <MailOutlined />
                    {selectedBooking.contactEmail}
                  </Space>
                </Descriptions.Item>
              )}
              {selectedBooking.specialRequirements && (
                <Descriptions.Item label="特殊要求">
                  <Space>
                    <FileTextOutlined />
                    {selectedBooking.specialRequirements}
                  </Space>
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>

          {/* 付款记录 */}
          <Card title="付款记录">
            <PackagePaymentList 
              packageBookingId={selectedBooking.id}
              showUserInfo={false}
              showBookingInfo={false}
            />
          </Card>
        </div>
      )}
    </Modal>
  );

  // 获取标签页数量
  const getTabCount = (tabKey) => {
    if (tabKey === 'all') return bookings.length;
    
    return bookings.filter(booking => {
      switch (tabKey) {
        case 'pending':
          return booking.status === 'PENDING';
        case 'confirmed':
          return booking.status === 'CONFIRMED';
        case 'paid':
          return booking.paymentStatus === 'PAID';
        case 'completed':
          return booking.status === 'COMPLETED';
        case 'cancelled':
          return booking.status === 'CANCELLED';
        default:
          return false;
      }
    }).length;
  };

  return (
    <div className="my-package-bookings-page">
      <div className="page-header mb-6">
        <Title level={2}>我的套餐预订</Title>
      </div>

      <Card>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          items={[
            {
              key: 'all',
              label: `全部 (${getTabCount('all')})`,
            },
            {
              key: 'pending',
              label: `待确认 (${getTabCount('pending')})`,
            },
            {
              key: 'confirmed',
              label: `已确认 (${getTabCount('confirmed')})`,
            },
            {
              key: 'paid',
              label: `已付款 (${getTabCount('paid')})`,
            },
            {
              key: 'completed',
              label: `已完成 (${getTabCount('completed')})`,
            },
            {
              key: 'cancelled',
              label: `已取消 (${getTabCount('cancelled')})`,
            }
          ]}
        />

        <Spin spinning={loading}>
          {bookings.length > 0 ? (
            <List
              className="bookings-list"
              itemLayout="vertical"
              dataSource={bookings}
              renderItem={renderBookingItem}
              pagination={{
                pageSize: 10,
                showSizeChanger: false,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
            />
          ) : (
            <Empty 
              description="暂无预订记录"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Button type="primary" onClick={() => navigate('/cultural-packages')}>
                浏览套餐
              </Button>
            </Empty>
          )}
        </Spin>
      </Card>

      {/* 预订详情模态框 */}
      {renderDetailModal()}

      {/* 付款模态框 */}
      <PackagePaymentModal
        visible={paymentModalVisible}
        onCancel={() => setPaymentModalVisible(false)}
        onSuccess={handlePaymentSuccess}
        bookingData={selectedBooking}
      />
    </div>
  );
};

export default MyPackageBookings;
