import React, { useState } from 'react';
import { Card, Button, Space, Typography, message, Row, Col, Divider, Tag, Alert } from 'antd';
import { PackagePaymentModal } from '../components/PackagePayment';
import {
  CreditCardOutlined,
  DollarOutlined,
  CalendarOutlined,
  UserOutlined,
  PhoneOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

/**
 * 测试套餐付款功能页面
 */
const TestPackagePayment = () => {
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  const [selectedScenario, setSelectedScenario] = useState(0);

  // 多种测试场景的模拟预订数据（使用null ID来避免API调用）
  const mockBookingScenarios = [
    {
      id: null, // 使用null避免真实API调用
      bookingNumber: 'PKG20241206001',
      packageName: '甘孜藏族文化体验套餐',
      totalAmount: 1998.00,
      finalAmount: 1998.00,
      participantCount: 2,
      bookingDate: '2024-12-15',
      contactName: '张三',
      contactPhone: '13800138000',
      status: 'CONFIRMED',
      paymentStatus: 'PENDING',
      description: '标准文化套餐 - 中等价格'
    },
    {
      id: null, // 使用null避免真实API调用
      bookingNumber: 'PKG20241206002',
      packageName: '稻城亚丁深度摄影套餐',
      totalAmount: 3580.00,
      finalAmount: 3580.00,
      participantCount: 1,
      bookingDate: '2024-12-20',
      contactName: '李四',
      contactPhone: '13900139000',
      status: 'CONFIRMED',
      paymentStatus: 'PENDING',
      description: '高端摄影套餐 - 高价格'
    },
    {
      id: null, // 使用null避免真实API调用
      bookingNumber: 'PKG20241206003',
      packageName: '康定情歌一日游',
      totalAmount: 299.00,
      finalAmount: 299.00,
      participantCount: 4,
      bookingDate: '2024-12-12',
      contactName: '王五',
      contactPhone: '13700137000',
      status: 'CONFIRMED',
      paymentStatus: 'PENDING',
      description: '经济型套餐 - 低价格'
    }
  ];

  // 付款成功回调
  const handlePaymentSuccess = (payment) => {
    message.success('付款成功！');
    console.log('Payment success:', payment);
    setPaymentModalVisible(false);
  };

  // 选择测试场景
  const handleScenarioSelect = (index) => {
    setSelectedScenario(index);
  };

  const currentBooking = mockBookingScenarios[selectedScenario];

  // 调试信息
  console.log('🔍 TestPackagePayment - currentBooking:', currentBooking);
  console.log('🔍 TestPackagePayment - currentBooking.id:', currentBooking.id);

  return (
    <div style={{
      padding: '24px',
      background: '#f5f5f5',
      minHeight: '100vh'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: '32px' }}>
          <CreditCardOutlined style={{ marginRight: '12px', color: '#1890ff' }} />
          文化套餐付款页面排版测试
        </Title>

        {/* 测试场景选择 */}
        <Card
          title="选择测试场景"
          style={{ marginBottom: '24px' }}
          extra={<Tag color="blue">当前场景: {selectedScenario + 1}</Tag>}
        >
          <Row gutter={[16, 16]}>
            {mockBookingScenarios.map((scenario, index) => (
              <Col xs={24} sm={8} key={scenario.id}>
                <Card
                  size="small"
                  hoverable
                  className={selectedScenario === index ? 'selected-scenario' : ''}
                  onClick={() => handleScenarioSelect(index)}
                  style={{
                    border: selectedScenario === index ? '2px solid #1890ff' : '1px solid #d9d9d9',
                    cursor: 'pointer'
                  }}
                >
                  <div style={{ textAlign: 'center' }}>
                    <Title level={5} style={{ margin: '0 0 8px 0' }}>
                      场景 {index + 1}
                    </Title>
                    <Text type="secondary">{scenario.description}</Text>
                    <Divider style={{ margin: '12px 0' }} />
                    <Text strong style={{ color: '#cf1322', fontSize: '18px' }}>
                      ¥{scenario.totalAmount}
                    </Text>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Card>

        {/* 当前预订信息展示 */}
        <Card
          title="当前预订信息"
          style={{ marginBottom: '24px' }}
          extra={
            <Space>
              <Tag color="green">{currentBooking.status}</Tag>
              <Tag color="orange">{currentBooking.paymentStatus}</Tag>
            </Space>
          }
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Space direction="vertical" size="small">
                <Text type="secondary">预订编号</Text>
                <Text strong>{currentBooking.bookingNumber}</Text>
              </Space>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Space direction="vertical" size="small">
                <Text type="secondary">套餐名称</Text>
                <Text strong>{currentBooking.packageName}</Text>
              </Space>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Space direction="vertical" size="small">
                <Text type="secondary">
                  <CalendarOutlined /> 预订日期
                </Text>
                <Text strong>{currentBooking.bookingDate}</Text>
              </Space>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Space direction="vertical" size="small">
                <Text type="secondary">
                  <UserOutlined /> 参与人数
                </Text>
                <Text strong>{currentBooking.participantCount}人</Text>
              </Space>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Space direction="vertical" size="small">
                <Text type="secondary">
                  <PhoneOutlined /> 联系人
                </Text>
                <Text strong>{currentBooking.contactName}</Text>
              </Space>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Space direction="vertical" size="small">
                <Text type="secondary">
                  <DollarOutlined /> 付款金额
                </Text>
                <Text strong style={{ color: '#cf1322', fontSize: '20px' }}>
                  ¥{currentBooking.totalAmount}
                </Text>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 测试按钮 */}
        <Card>
          <div style={{ textAlign: 'center' }}>
            <Space direction="vertical" size="large">
              <div>
                <Button
                  type="primary"
                  size="large"
                  icon={<CreditCardOutlined />}
                  onClick={() => setPaymentModalVisible(true)}
                  style={{
                    height: '48px',
                    fontSize: '16px',
                    padding: '0 32px',
                    borderRadius: '8px'
                  }}
                >
                  打开付款页面测试排版效果
                </Button>
              </div>

              <div>
                <Text type="secondary">
                  点击按钮查看优化后的付款模态框排版布局
                </Text>
              </div>

              <Alert
                message="演示模式说明"
                description={
                  <div>
                    <p>• 这是纯前端演示模式，不会产生真实付款</p>
                    <p>• 可以体验完整的付款界面和交互效果</p>
                    <p>• 支付宝/微信支付会显示二维码界面</p>
                    <p>• 信用卡/银行卡会显示表单输入界面</p>
                    <p>• 所有付款操作都会模拟成功完成</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ textAlign: 'left', maxWidth: '500px', margin: '0 auto' }}
              />
            </Space>
          </div>
        </Card>

        {/* 付款模态框 */}
        <PackagePaymentModal
          visible={paymentModalVisible}
          onCancel={() => setPaymentModalVisible(false)}
          onSuccess={handlePaymentSuccess}
          bookingData={currentBooking}
        />
      </div>
    </div>
  );
};

export default TestPackagePayment;
