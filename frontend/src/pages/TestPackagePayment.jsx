import React, { useState } from 'react';
import { Card, Button, Space, Typography, message } from 'antd';
import { PackagePaymentModal } from '../components/PackagePayment';

const { Title } = Typography;

/**
 * 测试套餐付款功能页面
 */
const TestPackagePayment = () => {
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);

  // 模拟预订数据
  const mockBookingData = {
    id: 1,
    bookingNumber: 'PKG20241206001',
    packageName: '测试文化套餐',
    totalAmount: 299.00,
    finalAmount: 299.00,
    participantCount: 2,
    bookingDate: '2024-12-10',
    contactName: '张三',
    contactPhone: '13800138000',
    status: 'CONFIRMED',
    paymentStatus: 'PENDING'
  };

  // 付款成功回调
  const handlePaymentSuccess = (payment) => {
    message.success('付款成功！');
    console.log('Payment success:', payment);
    setPaymentModalVisible(false);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>测试套餐付款功能</Title>
      
      <Card title="模拟预订信息" style={{ marginBottom: '24px' }}>
        <Space direction="vertical">
          <div>预订号: {mockBookingData.bookingNumber}</div>
          <div>套餐名称: {mockBookingData.packageName}</div>
          <div>预订日期: {mockBookingData.bookingDate}</div>
          <div>参与人数: {mockBookingData.participantCount}人</div>
          <div>付款金额: ¥{mockBookingData.totalAmount}</div>
          <div>预订状态: {mockBookingData.status}</div>
          <div>付款状态: {mockBookingData.paymentStatus}</div>
        </Space>
      </Card>

      <Card>
        <Space>
          <Button 
            type="primary" 
            onClick={() => setPaymentModalVisible(true)}
          >
            测试付款功能
          </Button>
        </Space>
      </Card>

      {/* 付款模态框 */}
      <PackagePaymentModal
        visible={paymentModalVisible}
        onCancel={() => setPaymentModalVisible(false)}
        onSuccess={handlePaymentSuccess}
        bookingData={mockBookingData}
      />
    </div>
  );
};

export default TestPackagePayment;
