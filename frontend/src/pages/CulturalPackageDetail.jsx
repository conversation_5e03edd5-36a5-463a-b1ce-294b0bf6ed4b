import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Typography,
  Button,
  Space,
  Tag,
  Divider,
  Carousel,
  Tabs,
  List,
  Badge,
  Spin,
  message,
  Modal,
  Form,
  InputNumber,
  DatePicker,
  Input,
  Breadcrumb,
  Statistic,
  Alert
} from 'antd';
import {
  ArrowLeftOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  UserOutlined,
  CalendarOutlined,
  StarOutlined,
  SafetyCertificateOutlined,
  ExclamationCircleOutlined,
  HeartOutlined,
  ShareAltOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useAuth } from '../context/AuthContext';
import culturalPackageService from '../services/culturalPackageService';
import packageBookingService from '../services/packageBookingService';
import { PackagePaymentModal } from '../components/PackagePayment';
import './CulturalPackageDetail.css';

const { Title, Text, Paragraph } = Typography;
// const { TabPane } = Tabs; // 已弃用，使用 items 属性
const { TextArea } = Input;

const CulturalPackageDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  
  // 状态管理
  const [packageDetail, setPackageDetail] = useState(null);
  const [similarPackages, setSimilarPackages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [bookingModalVisible, setBookingModalVisible] = useState(false);
  const [bookingLoading, setBookingLoading] = useState(false);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  const [bookingResult, setBookingResult] = useState(null);
  
  // 表单实例
  const [form] = Form.useForm();

  // 获取套餐详情
  const fetchPackageDetail = async () => {
    try {
      setLoading(true);
      const response = await culturalPackageService.getPackageById(id);
      
      if (response.success) {
        setPackageDetail(response.data);
        // 获取相似套餐
        fetchSimilarPackages(response.data.id);
      } else {
        message.error(response.message || '获取套餐详情失败');
      }
    } catch (error) {
      console.error('获取套餐详情失败:', error);
      message.error('获取套餐详情失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取相似套餐
  const fetchSimilarPackages = async (packageId) => {
    try {
      const response = await culturalPackageService.getSimilarPackages(packageId, 4);
      if (response.success) {
        setSimilarPackages(response.data);
      }
    } catch (error) {
      console.error('获取相似套餐失败:', error);
    }
  };

  // 初始化加载
  useEffect(() => {
    if (id) {
      fetchPackageDetail();
    }
  }, [id]);

  // 处理预订
  const handleBooking = () => {
    if (!isAuthenticated) {
      Modal.confirm({
        title: '需要登录',
        content: '请先登录后再进行预订',
        okText: '去登录',
        cancelText: '取消',
        onOk: () => navigate('/login')
      });
      return;
    }
    setBookingModalVisible(true);
  };

  // 提交预订
  const handleBookingSubmit = async (values) => {
    try {
      setBookingLoading(true);

      // 检查用户是否登录
      if (!isAuthenticated) {
        message.error('请先登录后再进行预订');
        return;
      }

      // 准备预订数据
      const bookingData = {
        bookingDate: values.preferredDate ? values.preferredDate.format('YYYY-MM-DD') : '',
        participantCount: values.participants || 1,
        contactName: values.contactName || '',
        contactPhone: values.contactPhone || '',
        contactEmail: values.contactEmail || '',
        specialRequirements: values.specialRequirements || '',
        participantInfo: values.participantInfo || ''
      };

      console.log('提交预订数据:', bookingData);

      // 调用预订API
      const response = await packageBookingService.createBooking(packageDetail.id, bookingData);

      if (response.success) {
        message.success('预订申请已提交成功！预订号：' + response.data.bookingNumber);
        setBookingModalVisible(false);
        form.resetFields();
        setBookingResult(response.data);

        // 如果预订状态是已确认，询问是否立即付款
        if (response.data.status === 'CONFIRMED') {
          Modal.confirm({
            title: '预订成功',
            content: (
              <div>
                <p>您的预订已确认，预订号：{response.data.bookingNumber}</p>
                <p>是否立即进行付款？</p>
              </div>
            ),
            okText: '立即付款',
            cancelText: '稍后付款',
            onOk: () => {
              setPaymentModalVisible(true);
            },
            onCancel: () => {
              navigate('/my-package-bookings');
            }
          });
        } else {
          // 显示预订详情
          Modal.info({
            title: '预订成功',
            width: 600,
            content: (
              <div style={{ marginTop: 16 }}>
                <p><strong>预订号：</strong>{response.data.bookingNumber}</p>
                <p><strong>套餐名称：</strong>{response.data.packageName}</p>
                <p><strong>预订日期：</strong>{response.data.bookingDate}</p>
                <p><strong>参与人数：</strong>{response.data.participantCount}人</p>
                <p><strong>联系人：</strong>{response.data.contactName}</p>
                <p><strong>联系电话：</strong>{response.data.contactPhone}</p>
                <p><strong>预订状态：</strong>{response.data.status === 'PENDING' ? '待确认' : response.data.status}</p>
                <p><strong>支付状态：</strong>{response.data.paymentStatus === 'PENDING' ? '待支付' : response.data.paymentStatus}</p>
                <p style={{ marginTop: 16, color: '#666' }}>
                  请保存好您的预订号，我们将尽快与您联系确认预订详情。
                </p>
            </div>
          ),
          onOk: () => {
            navigate('/my-package-bookings');
          }
        });
        }
      } else {
        message.error(response.message || '预订失败，请稍后重试');
      }
    } catch (error) {
      console.error('预订失败:', error);
      message.error(error.message || '预订失败，请稍后重试');
    } finally {
      setBookingLoading(false);
    }
  };

  // 付款成功回调
  const handlePaymentSuccess = (payment) => {
    message.success('付款成功！');
    setPaymentModalVisible(false);

    Modal.success({
      title: '付款成功',
      content: (
        <div>
          <p>付款编号：{payment.paymentNumber}</p>
          <p>付款金额：¥{payment.amount}</p>
          <p>您可以在"我的预订"中查看详细信息</p>
        </div>
      ),
      onOk: () => {
        navigate('/my-package-bookings');
      }
    });
  };

  // 处理收藏
  const handleFavorite = () => {
    // TODO: 实现收藏功能，需要后端API支持
    message.success('收藏功能开发中，敬请期待！');
  };

  // 处理分享
  const handleShare = () => {
    // 复制当前页面链接到剪贴板
    const url = window.location.href;
    if (navigator.clipboard) {
      navigator.clipboard.writeText(url).then(() => {
        message.success('链接已复制到剪贴板');
      }).catch(() => {
        message.error('复制失败，请手动复制链接');
      });
    } else {
      // 降级方案：显示链接让用户手动复制
      Modal.info({
        title: '分享链接',
        content: (
          <div>
            <p>请复制以下链接分享给朋友：</p>
            <Input value={url} readOnly />
          </div>
        ),
      });
    }
  };

  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };

  // 渲染图片轮播
  const renderImageCarousel = () => {
    if (!packageDetail?.images || packageDetail.images.length === 0) {
      return (
        <div className="package-placeholder-large">
          <StarOutlined style={{ fontSize: '64px', color: '#d9d9d9' }} />
          <Text type="secondary">暂无图片</Text>
        </div>
      );
    }

    return (
      <Carousel autoplay className="package-carousel">
        {packageDetail.images.map((image, index) => (
          <div key={index} className="carousel-slide">
            <img
              src={image}
              alt={`${packageDetail.name} - ${index + 1}`}
              onError={(e) => {
                // 如果图片加载失败，隐藏图片并显示占位符
                e.target.style.display = 'none';
                const placeholder = e.target.parentNode;
                placeholder.innerHTML = `
                  <div class="package-placeholder-large" style="
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 400px;
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    color: #999;
                  ">
                    <div style="font-size: 64px; margin-bottom: 16px;">📷</div>
                    <div>暂无图片</div>
                  </div>
                `;
              }}
            />
          </div>
        ))}
      </Carousel>
    );
  };

  // 渲染套餐亮点
  const renderHighlights = () => {
    if (!packageDetail?.highlights || packageDetail.highlights.length === 0) {
      return <Text type="secondary">暂无特色亮点</Text>;
    }

    return (
      <List
        dataSource={packageDetail.highlights}
        renderItem={(item) => (
          <List.Item>
            <Space>
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
              <Text>{item}</Text>
            </Space>
          </List.Item>
        )}
      />
    );
  };

  // 渲染包含项目
  const renderIncludes = () => {
    if (!packageDetail?.includes || packageDetail.includes.length === 0) {
      return <Text type="secondary">暂无包含项目</Text>;
    }

    return (
      <List
        dataSource={packageDetail.includes}
        renderItem={(item) => (
          <List.Item>
            <Space>
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
              <Text>{item}</Text>
            </Space>
          </List.Item>
        )}
      />
    );
  };

  // 渲染不包含项目
  const renderExcludes = () => {
    if (!packageDetail?.excludes || packageDetail.excludes.length === 0) {
      return <Text type="secondary">暂无不包含项目</Text>;
    }

    return (
      <List
        dataSource={packageDetail.excludes}
        renderItem={(item) => (
          <List.Item>
            <Space>
              <ExclamationCircleOutlined style={{ color: '#fa8c16' }} />
              <Text>{item}</Text>
            </Space>
          </List.Item>
        )}
      />
    );
  };

  // 渲染相似套餐
  const renderSimilarPackages = () => {
    if (similarPackages.length === 0) {
      return null;
    }

    return (
      <Card title="相似推荐" className="similar-packages-card">
        <Row gutter={[16, 16]}>
          {similarPackages.map(pkg => (
            <Col xs={24} sm={12} lg={6} key={pkg.id}>
              <Card
                hoverable
                size="small"
                cover={
                  <div className="similar-package-image">
                    {pkg.images && pkg.images.length > 0 ? (
                      <img
                        alt={pkg.name}
                        src={pkg.images[0]}
                        onError={(e) => {
                          // 如果图片加载失败，隐藏图片并显示占位符
                          e.target.style.display = 'none';
                          const placeholder = e.target.parentNode;
                          placeholder.innerHTML = `
                            <div class="similar-package-placeholder" style="
                              display: flex;
                              align-items: center;
                              justify-content: center;
                              height: 120px;
                              background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                              color: #999;
                            ">
                              <div style="font-size: 24px;">📷</div>
                            </div>
                          `;
                        }}
                      />
                    ) : (
                      <div className="similar-package-placeholder">
                        <StarOutlined style={{ fontSize: '24px', color: '#d9d9d9' }} />
                      </div>
                    )}
                  </div>
                }
                onClick={() => navigate(`/packages/${pkg.id}`)}
              >
                <Card.Meta
                  title={
                    <Text ellipsis={{ tooltip: pkg.name }} style={{ fontSize: '14px' }}>
                      {pkg.name}
                    </Text>
                  }
                  description={
                    <div>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {pkg.location}
                      </Text>
                      <br />
                      <Text strong style={{ color: '#f5222d', fontSize: '14px' }}>
                        ¥{pkg.price}
                      </Text>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="package-detail-loading">
        <Spin size="large" />
      </div>
    );
  }

  if (!packageDetail) {
    return (
      <div className="package-detail-error">
        <Alert
          message="套餐不存在"
          description="您访问的文化套餐不存在或已下架"
          type="error"
          showIcon
          action={
            <Button type="primary" onClick={() => navigate('/packages')}>
              返回套餐列表
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div className="package-detail-container">
      {/* 面包屑导航 */}
      <Breadcrumb className="package-breadcrumb">
        <Breadcrumb.Item>
          <Button type="link" onClick={() => navigate('/')} style={{ padding: 0 }}>
            首页
          </Button>
        </Breadcrumb.Item>
        <Breadcrumb.Item>
          <Button type="link" onClick={() => navigate('/packages')} style={{ padding: 0 }}>
            文化套餐
          </Button>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{packageDetail.name}</Breadcrumb.Item>
      </Breadcrumb>

      {/* 返回按钮 */}
      <Button 
        icon={<ArrowLeftOutlined />} 
        onClick={handleGoBack}
        className="back-button"
      >
        返回
      </Button>

      <Row gutter={[24, 24]}>
        {/* 左侧内容 */}
        <Col xs={24} lg={16}>
          {/* 图片轮播 */}
          <Card className="package-images-card">
            {renderImageCarousel()}
          </Card>

          {/* 套餐详情标签页 */}
          <Card className="package-details-card">
            <Tabs
              defaultActiveKey="description"
              size="large"
              items={[
                {
                  key: 'description',
                  label: '套餐介绍',
                  children: (
                    <div className="package-description-content">
                      <Paragraph style={{ fontSize: '16px', lineHeight: '1.8' }}>
                        {packageDetail.description}
                      </Paragraph>

                      {packageDetail.culturalSignificance && (
                        <>
                          <Title level={4}>文化意义</Title>
                          <Paragraph style={{ fontSize: '15px', lineHeight: '1.7' }}>
                            {packageDetail.culturalSignificance}
                          </Paragraph>
                        </>
                      )}
                    </div>
                  )
                },
                {
                  key: 'highlights',
                  label: '特色亮点',
                  children: (
                    <div className="package-highlights-content">
                      {renderHighlights()}
                    </div>
                  )
                },
                {
                  key: 'includes',
                  label: '包含项目',
                  children: (
                    <div className="package-includes-content">
                      {renderIncludes()}
                    </div>
                  )
                },
                {
                  key: 'excludes',
                  label: '不包含项目',
                  children: (
                    <div className="package-excludes-content">
                      {renderExcludes()}
                    </div>
                  )
                },
                {
                  key: 'notices',
                  label: '注意事项',
                  children: (
                    <div className="package-notices-content">
                      {packageDetail.physicalRequirements && (
                        <>
                          <Title level={5}>身体要求</Title>
                          <Paragraph>{packageDetail.physicalRequirements}</Paragraph>
                        </>
                      )}

                      {packageDetail.languageRequirements && (
                        <>
                          <Title level={5}>语言要求</Title>
                          <Paragraph>{packageDetail.languageRequirements}</Paragraph>
                        </>
                      )}

                      {packageDetail.bookingNotice && (
                        <>
                          <Title level={5}>预订须知</Title>
                          <Paragraph>{packageDetail.bookingNotice}</Paragraph>
                        </>
                      )}

                      {packageDetail.cancellationPolicy && (
                        <>
                          <Title level={5}>取消政策</Title>
                          <Paragraph>{packageDetail.cancellationPolicy}</Paragraph>
                        </>
                      )}
                    </div>
                  )
                }
              ]}
            />
          </Card>
        </Col>

        {/* 右侧预订卡片 */}
        <Col xs={24} lg={8}>
          <div className="package-booking-sidebar">
            <Card className="package-info-card">
              {/* 套餐标题和类别 */}
              <div className="package-header">
                <Title level={3} style={{ marginBottom: '8px' }}>
                  {packageDetail.name}
                </Title>
                <Space>
                  <Tag color="blue" style={{ fontSize: '14px', padding: '4px 12px' }}>
                    {culturalPackageService.formatCategoryName(packageDetail.category)}
                  </Tag>
                  {packageDetail.difficultyLevel && (
                    <Badge 
                      color={
                        packageDetail.difficultyLevel === 'EASY' ? 'green' : 
                        packageDetail.difficultyLevel === 'MODERATE' ? 'orange' : 'red'
                      }
                      text={culturalPackageService.formatDifficultyLevel(packageDetail.difficultyLevel)}
                    />
                  )}
                </Space>
              </div>

              <Divider />

              {/* 价格 */}
              <div className="package-price-section">
                <Statistic
                  title="套餐价格"
                  value={packageDetail.price}
                  prefix="¥"
                  valueStyle={{ color: '#f5222d', fontSize: '32px', fontWeight: 'bold' }}
                />
                <Text type="secondary">每人价格</Text>
              </div>

              <Divider />

              {/* 基本信息 */}
              <div className="package-basic-info">
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <div className="info-item">
                    <EnvironmentOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
                    <Text strong>地点：</Text>
                    <Text>{packageDetail.location}</Text>
                  </div>
                  
                  <div className="info-item">
                    <ClockCircleOutlined style={{ color: '#52c41a', marginRight: '8px' }} />
                    <Text strong>时长：</Text>
                    <Text>{packageDetail.durationHours} 小时</Text>
                  </div>
                  
                  <div className="info-item">
                    <UserOutlined style={{ color: '#fa8c16', marginRight: '8px' }} />
                    <Text strong>人数：</Text>
                    <Text>{packageDetail.minParticipants}-{packageDetail.maxParticipants} 人</Text>
                  </div>
                  
                  {packageDetail.bestSeason && (
                    <div className="info-item">
                      <CalendarOutlined style={{ color: '#722ed1', marginRight: '8px' }} />
                      <Text strong>最佳季节：</Text>
                      <Text>{packageDetail.bestSeason}</Text>
                    </div>
                  )}
                </Space>
              </div>

              <Divider />

              {/* 预订按钮 */}
              <div className="package-booking-actions">
                <Button
                  type="primary"
                  size="large"
                  block
                  onClick={handleBooking}
                  style={{ 
                    height: '48px', 
                    fontSize: '16px', 
                    fontWeight: 'bold',
                    marginBottom: '12px'
                  }}
                >
                  立即预订
                </Button>
                
                <Space style={{ width: '100%', justifyContent: 'center' }}>
                  <Button
                    icon={<HeartOutlined />}
                    type="text"
                    onClick={handleFavorite}
                  >
                    收藏
                  </Button>
                  <Button
                    icon={<ShareAltOutlined />}
                    type="text"
                    onClick={handleShare}
                  >
                    分享
                  </Button>
                </Space>
              </div>

              {/* 安全保障 */}
              <div className="package-safety">
                <Space>
                  <SafetyCertificateOutlined style={{ color: '#52c41a' }} />
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    专业导游 · 安全保障 · 品质服务
                  </Text>
                </Space>
              </div>
            </Card>
          </div>
        </Col>
      </Row>

      {/* 相似推荐 */}
      {renderSimilarPackages()}

      {/* 预订模态框 */}
      <Modal
        title="预订文化套餐"
        open={bookingModalVisible}
        onCancel={() => setBookingModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleBookingSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="participants"
                label="参与人数"
                rules={[
                  { required: true, message: '请选择参与人数' },
                  { 
                    type: 'number', 
                    min: packageDetail.minParticipants, 
                    max: packageDetail.maxParticipants,
                    message: `人数范围：${packageDetail.minParticipants}-${packageDetail.maxParticipants}人`
                  }
                ]}
              >
                <InputNumber
                  min={packageDetail.minParticipants}
                  max={packageDetail.maxParticipants}
                  style={{ width: '100%' }}
                  placeholder="选择人数"
                />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="preferredDate"
                label="期望日期"
                rules={[{ required: true, message: '请选择期望日期' }]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  placeholder="选择日期"
                  disabledDate={(current) => current && current.valueOf() < Date.now()}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="contactPhone"
            label="联系电话"
            rules={[
              { required: true, message: '请输入联系电话' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
            ]}
          >
            <Input placeholder="请输入您的联系电话" />
          </Form.Item>
          
          <Form.Item
            name="specialRequests"
            label="特殊要求"
          >
            <TextArea
              rows={3}
              placeholder="如有特殊要求或需要，请在此说明"
              maxLength={200}
            />
          </Form.Item>
          
          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setBookingModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={bookingLoading}>
                提交预订
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 付款模态框 */}
      <PackagePaymentModal
        visible={paymentModalVisible}
        onCancel={() => setPaymentModalVisible(false)}
        onSuccess={handlePaymentSuccess}
        bookingData={bookingResult}
      />
    </div>
  );
};

export default CulturalPackageDetail;
