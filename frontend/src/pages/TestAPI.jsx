import React, { useState, useEffect } from 'react';
import { <PERSON>ton, Card, List, Spin, Alert, Tabs } from 'antd';
import { hotelService } from '../services/hotelService';
import api from '../services/api';

const TestAPI = () => {
  const [loading, setLoading] = useState(false);
  const [hotels, setHotels] = useState([]);
  const [error, setError] = useState(null);
  const [logs, setLogs] = useState([]);
  const [reportData, setReportData] = useState(null);

  const addLog = (message) => {
    console.log(message);
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testAPI = async () => {
    setLoading(true);
    setError(null);
    setHotels([]);
    setLogs([]);

    try {
      addLog('开始测试API...');

      const response = await hotelService.getAllHotels();
      addLog(`API响应: ${JSON.stringify(response, null, 2)}`);

      if (response.success) {
        const hotelList = response.data.content || [];
        setHotels(hotelList);
        addLog(`成功获取 ${hotelList.length} 个酒店`);
      } else {
        setError(response.message || 'API返回失败');
        addLog(`API返回失败: ${response.message}`);
      }
    } catch (err) {
      console.error('API测试失败:', err);
      setError(err.message);
      addLog(`API测试失败: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };



  useEffect(() => {
    testAPI();
  }, []);

  return (
    <div style={{ padding: '20px' }}>
      <Card title="API测试页面" style={{ marginBottom: '20px' }}>
        <div style={{ marginBottom: '20px' }}>
          <Button
            type="primary"
            onClick={testAPI}
            loading={loading}
            style={{ marginRight: '10px' }}
          >
            测试酒店API
          </Button>
          <Button
            type="primary"
            onClick={testReportAPI}
            loading={loading}
          >
            测试报表API
          </Button>
        </div>
        
        {error && (
          <Alert 
            message="错误" 
            description={error} 
            type="error" 
            style={{ marginBottom: '20px' }} 
          />
        )}
        
        <div style={{ marginBottom: '20px' }}>
          <h3>控制台日志:</h3>
          <div style={{ 
            background: '#f5f5f5', 
            padding: '10px', 
            borderRadius: '4px',
            maxHeight: '200px',
            overflow: 'auto',
            fontFamily: 'monospace',
            fontSize: '12px'
          }}>
            {logs.map((log, index) => (
              <div key={index}>{log}</div>
            ))}
          </div>
        </div>
        
        <Spin spinning={loading}>
          <Tabs defaultActiveKey="1" items={[
            {
              key: '1',
              label: '酒店数据',
              children: (
                <div>
                  <h3>酒店列表 ({hotels.length}个):</h3>
                  <List
                    dataSource={hotels}
                    renderItem={(hotel) => (
                      <List.Item>
                        <List.Item.Meta
                          title={hotel.name}
                          description={`${hotel.city} - ${hotel.starRating}星 - ¥${hotel.minPrice || 0}-${hotel.maxPrice || 0}`}
                        />
                      </List.Item>
                    )}
                  />
                </div>
              )
            },
            {
              key: '2',
              label: '报表数据',
              children: (
                <div>
                  <h3>报表数据:</h3>
                  {reportData ? (
                    <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
                      {JSON.stringify(reportData, null, 2)}
                    </pre>
                  ) : (
                    <p>暂无报表数据，请点击"测试报表API"按钮</p>
                  )}
                </div>
              )
            }
          ]} />
        </Spin>
      </Card>
    </div>
  );
};

export default TestAPI;
