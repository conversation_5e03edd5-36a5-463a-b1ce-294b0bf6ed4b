import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Button, Result, Spin, message, Space, Typography, Descriptions } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined, CreditCardOutlined } from '@ant-design/icons';
import packagePaymentService from '../services/packagePaymentService';

const { Title, Text } = Typography;

const PaymentSimulator = () => {
  const { paymentNumber } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [paymentData, setPaymentData] = useState(null);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    if (paymentNumber) {
      fetchPaymentDetails();
    }
  }, [paymentNumber]);

  const fetchPaymentDetails = async () => {
    try {
      setLoading(true);
      const response = await packagePaymentService.getPaymentDetails(paymentNumber);
      if (response.success) {
        setPaymentData(response.data);
      } else {
        message.error(response.message || '获取支付详情失败');
      }
    } catch (error) {
      console.error('获取支付详情失败:', error);
      message.error('获取支付详情失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSimulateSuccess = async () => {
    try {
      setProcessing(true);
      // 调用后端API模拟支付成功
      await simulatePaymentSuccess();
      message.success('支付成功！');
      
      // 显示成功结果
      setTimeout(() => {
        navigate('/my-package-bookings', { 
          state: { message: '支付成功，预订已确认！' }
        });
      }, 2000);
    } catch (error) {
      console.error('模拟支付成功失败:', error);
      message.error('模拟支付失败');
    } finally {
      setProcessing(false);
    }
  };

  const handleSimulateFailure = async () => {
    try {
      setProcessing(true);
      // 调用后端API模拟支付失败
      await simulatePaymentFailure();
      message.error('支付失败！');
      
      // 显示失败结果
      setTimeout(() => {
        navigate('/my-package-bookings', { 
          state: { message: '支付失败，请重试' }
        });
      }, 2000);
    } catch (error) {
      console.error('模拟支付失败失败:', error);
      message.error('模拟支付失败');
    } finally {
      setProcessing(false);
    }
  };

  const simulatePaymentSuccess = async () => {
    const response = await packagePaymentService.simulatePaymentSuccess(paymentNumber);
    if (!response.success) {
      throw new Error(response.message || '模拟支付成功失败');
    }
    return response.data;
  };

  const simulatePaymentFailure = async () => {
    const response = await packagePaymentService.simulatePaymentFailure(paymentNumber);
    if (!response.success) {
      throw new Error(response.message || '模拟支付失败失败');
    }
    return response.data;
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '400px' 
      }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!paymentData) {
    return (
      <Result
        status="error"
        title="支付信息不存在"
        subTitle="无法找到对应的支付信息"
        extra={
          <Button type="primary" onClick={() => navigate('/my-package-bookings')}>
            返回预订列表
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ maxWidth: 600, margin: '0 auto', padding: '20px' }}>
      <Card>
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <CreditCardOutlined style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} />
          <Title level={3}>支付模拟器</Title>
          <Text type="secondary">这是一个测试环境，您可以模拟支付成功或失败</Text>
        </div>

        <Descriptions title="支付详情" bordered column={1}>
          <Descriptions.Item label="支付单号">{paymentData.paymentNumber}</Descriptions.Item>
          <Descriptions.Item label="套餐名称">{paymentData.packageName || '文化套餐'}</Descriptions.Item>
          <Descriptions.Item label="支付金额">
            ¥{paymentData.amount} {paymentData.currency}
          </Descriptions.Item>
          <Descriptions.Item label="支付方式">
            {paymentData.paymentMethodDescription || paymentData.paymentMethod}
          </Descriptions.Item>
          <Descriptions.Item label="当前状态">
            <Text type={paymentData.status === 'PENDING' ? 'warning' : 'default'}>
              {paymentData.statusDescription || paymentData.status}
            </Text>
          </Descriptions.Item>
          {paymentData.packageBookingNumber && (
            <Descriptions.Item label="预订单号">{paymentData.packageBookingNumber}</Descriptions.Item>
          )}
          {paymentData.createdAt && (
            <Descriptions.Item label="创建时间">{paymentData.createdAt}</Descriptions.Item>
          )}
        </Descriptions>

        <div style={{ marginTop: '32px', textAlign: 'center' }}>
          <Space size="large">
            <Button
              type="primary"
              size="large"
              icon={<CheckCircleOutlined />}
              loading={processing}
              onClick={handleSimulateSuccess}
              style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
            >
              模拟支付成功
            </Button>
            <Button
              size="large"
              icon={<CloseCircleOutlined />}
              loading={processing}
              onClick={handleSimulateFailure}
              danger
            >
              模拟支付失败
            </Button>
          </Space>
        </div>

        <div style={{ marginTop: '24px', textAlign: 'center' }}>
          <Button type="link" onClick={() => navigate('/my-package-bookings')}>
            返回预订列表
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default PaymentSimulator;
