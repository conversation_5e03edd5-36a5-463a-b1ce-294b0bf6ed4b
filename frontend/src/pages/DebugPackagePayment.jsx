import React, { useState } from 'react';
import { Card, Button, Space, Typography, message, Input, Form, Select } from 'antd';
import { packagePaymentService } from '../services/packagePaymentService';

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * 调试套餐付款功能页面
 */
const DebugPackagePayment = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [form] = Form.useForm();

  // 测试创建付款
  const testCreatePayment = async (values) => {
    setLoading(true);
    setResult(null);
    
    try {
      console.log('Testing create payment with values:', values);
      
      const paymentData = {
        amount: parseFloat(values.amount),
        currency: 'CNY',
        paymentMethod: values.paymentMethod,
        returnUrl: `${window.location.origin}/package-booking/payment/return`,
        notifyUrl: `${window.location.origin}/api/packages/payments/callback`,
        remarks: `测试套餐预订付款`
      };

      console.log('Payment data:', paymentData);
      
      const response = await packagePaymentService.createPayment(values.bookingId, paymentData);
      
      console.log('API Response:', response);
      setResult(JSON.stringify(response, null, 2));
      
      if (response.success) {
        message.success('付款创建成功');
      } else {
        message.error(response.message || '付款创建失败');
      }
    } catch (error) {
      console.error('Create payment error:', error);
      setResult(JSON.stringify({
        error: error.message,
        stack: error.stack,
        response: error.response?.data
      }, null, 2));
      message.error('付款创建失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 测试查询付款
  const testGetPayment = async (paymentNumber) => {
    setLoading(true);
    setResult(null);
    
    try {
      console.log('Testing get payment:', paymentNumber);
      
      const response = await packagePaymentService.getPayment(paymentNumber);
      
      console.log('API Response:', response);
      setResult(JSON.stringify(response, null, 2));
      
      if (response.success) {
        message.success('查询成功');
      } else {
        message.error(response.message || '查询失败');
      }
    } catch (error) {
      console.error('Get payment error:', error);
      setResult(JSON.stringify({
        error: error.message,
        stack: error.stack,
        response: error.response?.data
      }, null, 2));
      message.error('查询失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>调试套餐付款功能</Title>
      
      <Card title="创建付款测试" style={{ marginBottom: '24px' }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={testCreatePayment}
          initialValues={{
            bookingId: 1,
            amount: 299.00,
            paymentMethod: 'ALIPAY'
          }}
        >
          <Form.Item
            name="bookingId"
            label="预订ID"
            rules={[{ required: true, message: '请输入预订ID' }]}
          >
            <Input type="number" placeholder="输入预订ID" />
          </Form.Item>
          
          <Form.Item
            name="amount"
            label="付款金额"
            rules={[{ required: true, message: '请输入付款金额' }]}
          >
            <Input type="number" step="0.01" placeholder="输入付款金额" />
          </Form.Item>
          
          <Form.Item
            name="paymentMethod"
            label="付款方式"
            rules={[{ required: true, message: '请选择付款方式' }]}
          >
            <Select>
              <Select.Option value="ALIPAY">支付宝</Select.Option>
              <Select.Option value="WECHAT_PAY">微信支付</Select.Option>
              <Select.Option value="CREDIT_CARD">信用卡</Select.Option>
              <Select.Option value="BANK_CARD">银行卡</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              测试创建付款
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Card title="查询付款测试" style={{ marginBottom: '24px' }}>
        <Space>
          <Input 
            placeholder="输入付款编号" 
            id="paymentNumber"
            style={{ width: 200 }}
          />
          <Button 
            onClick={() => {
              const paymentNumber = document.getElementById('paymentNumber').value;
              if (paymentNumber) {
                testGetPayment(paymentNumber);
              } else {
                message.warning('请输入付款编号');
              }
            }}
            loading={loading}
          >
            查询付款
          </Button>
        </Space>
      </Card>

      <Card title="API响应结果">
        {result ? (
          <TextArea
            value={result}
            rows={20}
            readOnly
            style={{ fontFamily: 'monospace' }}
          />
        ) : (
          <Text type="secondary">暂无结果</Text>
        )}
      </Card>

      <Card title="快速测试链接" style={{ marginTop: '24px' }}>
        <Space>
          <Button onClick={() => window.open('/test-payment', '_blank')}>
            打开付款测试页面
          </Button>
          <Button onClick={() => window.open('/my-package-bookings', '_blank')}>
            打开我的套餐预订
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default DebugPackagePayment;
