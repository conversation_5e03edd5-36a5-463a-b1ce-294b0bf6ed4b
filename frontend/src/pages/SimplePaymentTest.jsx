import React, { useState } from 'react';
import { Button, Card, Typography, message } from 'antd';
import { PackagePaymentModal } from '../components/PackagePayment';

const { Title } = Typography;

/**
 * 简单的付款测试页面 - 专门用于调试演示模式
 */
const SimplePaymentTest = () => {
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);

  // 确保ID为null的测试数据
  const testBookingData = {
    id: null, // 明确设置为null
    bookingNumber: 'TEST-DEMO-001',
    packageName: '演示模式测试套餐',
    totalAmount: 999.00,
    finalAmount: 999.00,
    participantCount: 1,
    bookingDate: '2024-12-15',
    contactName: '测试用户',
    contactPhone: '13800138000',
    status: 'CONFIRMED',
    paymentStatus: 'PENDING'
  };

  // 调试信息
  console.log('🔍 SimplePaymentTest - testBookingData:', testBookingData);
  console.log('🔍 SimplePaymentTest - testBookingData.id:', testBookingData.id);
  console.log('🔍 SimplePaymentTest - typeof testBookingData.id:', typeof testBookingData.id);
  console.log('🔍 SimplePaymentTest - !testBookingData?.id:', !testBookingData?.id);

  const handlePaymentSuccess = (payment) => {
    message.success('演示付款成功！');
    console.log('Payment success:', payment);
    setPaymentModalVisible(false);
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>简单付款测试页面</Title>
      
      <Card>
        <div style={{ textAlign: 'center' }}>
          <p>这是一个专门用于测试演示模式的简单页面</p>
          <p>预订ID: <strong>{String(testBookingData.id)}</strong></p>
          <p>预订ID类型: <strong>{typeof testBookingData.id}</strong></p>
          <p>演示模式检测: <strong>{!testBookingData?.id ? '是' : '否'}</strong></p>
          
          <Button 
            type="primary" 
            size="large"
            onClick={() => {
              console.log('🚀 打开付款模态框，传递数据:', testBookingData);
              setPaymentModalVisible(true);
            }}
            style={{ marginTop: '20px' }}
          >
            测试付款模态框
          </Button>
        </div>
      </Card>

      <PackagePaymentModal
        visible={paymentModalVisible}
        onCancel={() => setPaymentModalVisible(false)}
        onSuccess={handlePaymentSuccess}
        bookingData={testBookingData}
      />
    </div>
  );
};

export default SimplePaymentTest;
