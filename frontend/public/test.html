<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🔧 前端系统连接测试</h1>
    
    <div class="status success">
        <h3>✅ 基础连接正常</h3>
        <p>如果您能看到这个页面，说明前端服务正在正常运行。</p>
        <p><strong>当前时间:</strong> <span id="current-time"></span></p>
        <p><strong>服务地址:</strong> http://localhost:3003</p>
    </div>

    <div class="status info">
        <h3>🌐 网络连接测试</h3>
        <button onclick="testConnection()">测试网络连接</button>
        <div id="connection-result"></div>
    </div>

    <div class="status info">
        <h3>🔌 后端API测试</h3>
        <button onclick="testBackendAPI()">测试后端连接</button>
        <div id="api-result"></div>
    </div>

    <div class="status info">
        <h3>📦 文化套餐API测试</h3>
        <button onclick="testPackagesAPI()">测试套餐API</button>
        <div id="packages-result"></div>
    </div>

    <div class="status info">
        <h3>🔄 React应用测试</h3>
        <p>主React应用地址: <a href="/" target="_blank">http://localhost:3003/</a></p>
        <button onclick="testReactApp()">测试React应用</button>
        <div id="react-result"></div>
    </div>

    <script>
        // 显示当前时间
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
        updateTime();
        setInterval(updateTime, 1000);

        // 测试基础连接
        async function testConnection() {
            const resultDiv = document.getElementById('connection-result');
            resultDiv.innerHTML = '<p>正在测试连接...</p>';
            
            try {
                const response = await fetch('/test.html');
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success"><p>✅ 网络连接正常</p></div>';
                } else {
                    resultDiv.innerHTML = '<div class="error"><p>❌ 连接异常</p></div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error"><p>❌ 连接失败: ${error.message}</p></div>`;
            }
        }

        // 测试后端API
        async function testBackendAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<p>正在测试后端API...</p>';
            
            try {
                const response = await fetch('http://localhost:8080/api/packages');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <p>✅ 后端API连接成功</p>
                            <p>获取到 ${data.data.length} 个文化套餐</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <p>❌ API返回错误: ${data.message}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <p>❌ 后端连接失败: ${error.message}</p>
                        <p>请检查后端服务是否在 http://localhost:8080 运行</p>
                    </div>
                `;
            }
        }

        // 测试文化套餐API
        async function testPackagesAPI() {
            const resultDiv = document.getElementById('packages-result');
            resultDiv.innerHTML = '<p>正在测试套餐API...</p>';
            
            try {
                const response = await fetch('http://localhost:8080/api/packages');
                const data = await response.json();
                
                if (data.success && data.data.length > 0) {
                    const firstPackage = data.data[0];
                    resultDiv.innerHTML = `
                        <div class="success">
                            <p>✅ 文化套餐API正常</p>
                            <p><strong>示例套餐:</strong> ${firstPackage.name}</p>
                            <p><strong>价格:</strong> ¥${firstPackage.price}</p>
                            <p><strong>地点:</strong> ${firstPackage.location}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <p>❌ 没有获取到套餐数据</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <p>❌ 套餐API测试失败: ${error.message}</p>
                    </div>
                `;
            }
        }

        // 测试React应用
        async function testReactApp() {
            const resultDiv = document.getElementById('react-result');
            resultDiv.innerHTML = '<p>正在测试React应用...</p>';
            
            try {
                const response = await fetch('/');
                const html = await response.text();
                
                if (html.includes('id="root"')) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <p>✅ React应用HTML结构正常</p>
                            <p>请点击上方链接访问主应用</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <p>❌ React应用HTML结构异常</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <p>❌ React应用测试失败: ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
