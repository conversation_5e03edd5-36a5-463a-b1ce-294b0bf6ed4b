package com.ganzi.hotel.config;

import com.ganzi.hotel.security.CustomUserDetailsService;
import com.ganzi.hotel.security.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

/**
 * Spring Security配置类
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private CustomUserDetailsService userDetailsService;

    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    /**
     * 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 认证提供者
     */
    @Bean
    public AuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    /**
     * 认证管理器
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    /**
     * CORS配置
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(List.of("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    /**
     * 安全过滤器链配置
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                // 禁用CSRF（使用JWT时不需要）
                .csrf(AbstractHttpConfigurer::disable)

                // 配置CORS
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))

                // 配置会话管理为无状态
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))

                // 配置授权规则
                .authorizeHttpRequests(authz -> authz
                        // 公开端点
                        .requestMatchers("/auth/**").permitAll()
                        .requestMatchers("/health").permitAll()
                        .requestMatchers("/public/**").permitAll()

                        // 仪表板API（需要管理员权限）
                        .requestMatchers("/admin/dashboard/**").hasRole("ADMIN")
                        .requestMatchers("/api/admin/dashboard/**").hasRole("ADMIN")

                        // Actuator端点
                        .requestMatchers("/actuator/health").permitAll()
                        .requestMatchers("/actuator/**").hasRole("ADMIN")

                        // API文档端点
                        .requestMatchers("/swagger-ui/**", "/v3/api-docs/**").permitAll()

                        // 文件上传和访问端点（优先级最高）
                        .requestMatchers("/api/upload/**").permitAll()
                        .requestMatchers("/api/uploads/**").permitAll()
                        .requestMatchers("/upload/**").permitAll()
                        .requestMatchers("/uploads/**").permitAll()

                        // 酒店相关端点（部分公开）
                        .requestMatchers("/hotels/**").permitAll()
                        .requestMatchers("/api/hotels/**").permitAll()
                        .requestMatchers(HttpMethod.GET, "/rooms/**").permitAll() // 只允许GET请求公开访问
                        .requestMatchers(HttpMethod.GET, "/api/rooms/**").permitAll() // 只允许GET请求公开访问
                        .requestMatchers(HttpMethod.PATCH, "/rooms/*/status").permitAll() // 房间状态更新开放访问
                        .requestMatchers(HttpMethod.PATCH, "/api/rooms/*/status").permitAll() // 房间状态更新开放访问

                        .requestMatchers("/room-types/**").permitAll()
                        .requestMatchers("/api/room-types/**").permitAll()
                        .requestMatchers("/reviews/**").permitAll()
                        .requestMatchers("/api/reviews/**").permitAll()
                        .requestMatchers("/bookings/search").permitAll()
                        .requestMatchers("/api/bookings/search").permitAll()
                        .requestMatchers("/bookings/availability").permitAll()
                        .requestMatchers("/api/bookings/availability").permitAll()
                        .requestMatchers("/bookings").permitAll() // 临时允许匿名创建预订
                        .requestMatchers("/api/bookings").permitAll() // 临时允许匿名创建预订

                        // 文化套餐端点（公开访问）
                        .requestMatchers("/cultural-packages/**").permitAll()
                        .requestMatchers("/packages/**").permitAll()
                        .requestMatchers("/api/packages/**").permitAll()

                        // 轮播图端点（公开访问）
                        .requestMatchers("/banners/active").permitAll()
                        .requestMatchers("/api/banners/active").permitAll()
                        .requestMatchers(HttpMethod.GET, "/banners/**").hasRole("ADMIN")
                        .requestMatchers(HttpMethod.GET, "/api/banners/**").hasRole("ADMIN")
                        .requestMatchers("/banners/**").hasRole("ADMIN")
                        .requestMatchers("/api/banners/**").hasRole("ADMIN")

                        // 管理员端点 - 更具体的规则必须在前面
                        // .requestMatchers("/admin/**").hasRole("ADMIN") // 临时注释掉用于测试
                        // .requestMatchers("/api/admin/**").hasRole("ADMIN") // 临时注释掉用于测试
                        .requestMatchers("/admin/**").permitAll() // 临时允许公开访问
                        .requestMatchers("/api/admin/**").permitAll() // 临时允许公开访问

                        // 测试端点（临时）
                        .requestMatchers("/test/**").permitAll()
                        .requestMatchers("/api/test/**").permitAll()
                        .requestMatchers("/test-reports/**").permitAll()
                        .requestMatchers("/simple-test/**").permitAll()
                        .requestMatchers("/api/simple-test/**").permitAll()

                        // 其他所有端点需要认证
                        .anyRequest().authenticated())

                // 设置认证提供者
                .authenticationProvider(authenticationProvider())

                // 添加JWT过滤器
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
