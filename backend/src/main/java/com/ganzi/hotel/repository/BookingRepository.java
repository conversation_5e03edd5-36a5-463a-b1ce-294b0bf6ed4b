package com.ganzi.hotel.repository;

import com.ganzi.hotel.entity.Booking;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 预订数据访问层
 */
@Repository
public interface BookingRepository extends JpaRepository<Booking, Long> {

       /**
        * 根据预订号查找预订
        */
       Optional<Booking> findByBookingNumber(String bookingNumber);

       /**
        * 根据用户ID查找预订
        */
       List<Booking> findByUserIdOrderByCreatedAtDesc(Long userId);

       /**
        * 根据用户ID分页查找预订
        */
       @Query("SELECT b FROM Booking b " +
                     "LEFT JOIN FETCH b.user " +
                     "LEFT JOIN FETCH b.hotel " +
                     "LEFT JOIN FETCH b.roomType " +
                     "LEFT JOIN FETCH b.room " +
                     "WHERE b.user.id = :userId " +
                     "ORDER BY b.createdAt DESC")
       Page<Booking> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);

       /**
        * 根据酒店ID查找预订
        */
       @Query("SELECT b FROM Booking b " +
                     "LEFT JOIN FETCH b.user " +
                     "LEFT JOIN FETCH b.hotel " +
                     "LEFT JOIN FETCH b.roomType " +
                     "LEFT JOIN FETCH b.room " +
                     "WHERE b.hotel.id = :hotelId " +
                     "ORDER BY b.createdAt DESC")
       List<Booking> findByHotelIdOrderByCreatedAtDesc(@Param("hotelId") Long hotelId);

       /**
        * 根据酒店ID分页查找预订
        */
       @Query("SELECT b FROM Booking b " +
                     "LEFT JOIN FETCH b.user " +
                     "LEFT JOIN FETCH b.hotel " +
                     "LEFT JOIN FETCH b.roomType " +
                     "LEFT JOIN FETCH b.room " +
                     "WHERE b.hotel.id = :hotelId " +
                     "ORDER BY b.createdAt DESC")
       Page<Booking> findByHotelIdOrderByCreatedAtDesc(@Param("hotelId") Long hotelId, Pageable pageable);

       /**
        * 根据用户ID、酒店ID和状态查找预订
        */
       List<Booking> findByUserIdAndHotelIdAndStatus(Long userId, Long hotelId, Booking.BookingStatus status);

       /**
        * 根据状态查找预订
        */
       List<Booking> findByStatusOrderByCreatedAtDesc(Booking.BookingStatus status);

       /**
        * 根据支付状态查找预订
        */
       List<Booking> findByPaymentStatusOrderByCreatedAtDesc(Booking.PaymentStatus paymentStatus);

       /**
        * 根据用户ID和状态查找预订
        */
       List<Booking> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, Booking.BookingStatus status);

       /**
        * 根据用户ID和状态查找预订（分页）
        */
       Page<Booking> findByUserIdAndStatusOrderByCreatedAtDesc(Long userId, Booking.BookingStatus status,
                     Pageable pageable);

       /**
        * 根据酒店ID和状态查找预订
        */
       List<Booking> findByHotelIdAndStatusOrderByCreatedAtDesc(Long hotelId, Booking.BookingStatus status);

       /**
        * 查找指定日期范围内的预订
        */
       @Query("SELECT b FROM Booking b WHERE " +
                     "b.checkInDate <= :endDate AND b.checkOutDate >= :startDate " +
                     "ORDER BY b.checkInDate")
       List<Booking> findBookingsInDateRange(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 查找指定房间在指定日期范围内的预订
        */
       @Query("SELECT b FROM Booking b WHERE " +
                     "b.room.id = :roomId AND " +
                     "b.status NOT IN ('CANCELLED', 'CHECKED_OUT') AND " +
                     "b.checkInDate <= :endDate AND b.checkOutDate >= :startDate " +
                     "ORDER BY b.checkInDate")
       List<Booking> findRoomBookingsInDateRange(@Param("roomId") Long roomId,
                     @Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 查找指定房型在指定日期范围内的预订
        */
       @Query("SELECT b FROM Booking b WHERE " +
                     "b.roomType.id = :roomTypeId AND " +
                     "b.status NOT IN ('CANCELLED', 'CHECKED_OUT') AND " +
                     "b.checkInDate <= :endDate AND b.checkOutDate >= :startDate " +
                     "ORDER BY b.checkInDate")
       List<Booking> findRoomTypeBookingsInDateRange(@Param("roomTypeId") Long roomTypeId,
                     @Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 查找今日入住的预订
        */
       @Query("SELECT b FROM Booking b WHERE b.checkInDate = :today AND b.status = 'CONFIRMED' ORDER BY b.createdAt")
       List<Booking> findTodayCheckIns(@Param("today") LocalDate today);

       /**
        * 查找今日退房的预订
        */
       @Query("SELECT b FROM Booking b WHERE b.checkOutDate = :today AND b.status = 'CHECKED_IN' ORDER BY b.createdAt")
       List<Booking> findTodayCheckOuts(@Param("today") LocalDate today);

       /**
        * 查找即将到期的预订（未支付）
        */
       @Query("SELECT b FROM Booking b WHERE " +
                     "b.paymentStatus = 'PENDING' AND " +
                     "b.status = 'PENDING' AND " +
                     "b.createdAt < :cutoffTime " +
                     "ORDER BY b.createdAt")
       List<Booking> findExpiredPendingBookings(@Param("cutoffTime") LocalDateTime cutoffTime);

       /**
        * 统计用户预订总数
        */
       @Query("SELECT COUNT(b) FROM Booking b WHERE b.user.id = :userId")
       long countByUserId(@Param("userId") Long userId);

       /**
        * 统计酒店预订总数
        */
       @Query("SELECT COUNT(b) FROM Booking b WHERE b.hotel.id = :hotelId")
       long countByHotelId(@Param("hotelId") Long hotelId);

       /**
        * 统计指定状态的预订数量
        */
       @Query("SELECT COUNT(b) FROM Booking b WHERE b.status = :status")
       long countByStatus(@Param("status") Booking.BookingStatus status);

       /**
        * 统计指定支付状态的预订数量
        */
       @Query("SELECT COUNT(b) FROM Booking b WHERE b.paymentStatus = :paymentStatus")
       long countByPaymentStatus(@Param("paymentStatus") Booking.PaymentStatus paymentStatus);

       /**
        * 计算用户总消费金额
        */
       @Query("SELECT COALESCE(SUM(b.finalAmount), 0) FROM Booking b WHERE b.user.id = :userId AND b.paymentStatus = 'PAID'")
       BigDecimal calculateTotalSpentByUser(@Param("userId") Long userId);

       /**
        * 计算酒店总收入
        */
       @Query("SELECT COALESCE(SUM(b.finalAmount), 0) FROM Booking b WHERE b.hotel.id = :hotelId AND b.paymentStatus = 'PAID'")
       BigDecimal calculateTotalRevenueByHotel(@Param("hotelId") Long hotelId);

       /**
        * 计算指定日期范围内的总收入
        */
       @Query("SELECT COALESCE(SUM(b.finalAmount), 0) FROM Booking b WHERE " +
                     "b.paymentStatus = 'PAID' AND " +
                     "b.createdAt BETWEEN :startDate AND :endDate")
       BigDecimal calculateRevenueInDateRange(@Param("startDate") LocalDateTime startDate,
                     @Param("endDate") LocalDateTime endDate);

       /**
        * 查找重复预订（同一用户在相同日期的预订）
        */
       @Query("SELECT b FROM Booking b WHERE " +
                     "b.user.id = :userId AND " +
                     "b.checkInDate = :checkInDate AND " +
                     "b.checkOutDate = :checkOutDate AND " +
                     "b.status NOT IN ('CANCELLED') " +
                     "ORDER BY b.createdAt DESC")
       List<Booking> findDuplicateBookings(@Param("userId") Long userId,
                     @Param("checkInDate") LocalDate checkInDate,
                     @Param("checkOutDate") LocalDate checkOutDate);

       /**
        * 检查预订号是否存在
        */
       boolean existsByBookingNumber(String bookingNumber);

       /**
        * 复合查询预订
        */
       @Query("SELECT b FROM Booking b WHERE " +
                     "(:userId IS NULL OR b.user.id = :userId) AND " +
                     "(:hotelId IS NULL OR b.hotel.id = :hotelId) AND " +
                     "(:status IS NULL OR b.status = :status) AND " +
                     "(:paymentStatus IS NULL OR b.paymentStatus = :paymentStatus) AND " +
                     "(:startDate IS NULL OR b.checkInDate >= :startDate) AND " +
                     "(:endDate IS NULL OR b.checkOutDate <= :endDate) " +
                     "ORDER BY b.createdAt DESC")
       Page<Booking> findBookingsWithFilters(@Param("userId") Long userId,
                     @Param("hotelId") Long hotelId,
                     @Param("status") Booking.BookingStatus status,
                     @Param("paymentStatus") Booking.PaymentStatus paymentStatus,
                     @Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate,
                     Pageable pageable);

       /**
        * 获取所有预订并预加载关联数据
        */
       @Query("SELECT b FROM Booking b " +
                     "LEFT JOIN FETCH b.user " +
                     "LEFT JOIN FETCH b.hotel " +
                     "LEFT JOIN FETCH b.roomType " +
                     "LEFT JOIN FETCH b.room " +
                     "ORDER BY b.createdAt DESC")
       List<Booking> findAllWithDetails();

       /**
        * 查找指定日期被预订的房间ID列表
        */
       @Query("SELECT DISTINCT b.room.id FROM Booking b WHERE " +
                     "b.hotel.id = :hotelId AND " +
                     "b.roomType.id = :roomTypeId AND " +
                     "b.room.id IS NOT NULL AND " +
                     "b.status NOT IN ('CANCELLED', 'CHECKED_OUT') AND " +
                     ":date >= b.checkInDate AND :date < b.checkOutDate")
       List<Long> findBookedRoomIdsByDateAndRoomType(@Param("hotelId") Long hotelId,
                     @Param("roomTypeId") Long roomTypeId,
                     @Param("date") LocalDate date);

       // ==================== 报表相关查询方法 ====================

       /**
        * 按日期范围查询收入数据
        */
       @Query("SELECT DATE(b.createdAt) as date, COALESCE(SUM(b.finalAmount), 0) as revenue, COUNT(b) as bookingCount "
                     +
                     "FROM Booking b WHERE b.paymentStatus = 'PAID' AND " +
                     "DATE(b.createdAt) BETWEEN :startDate AND :endDate " +
                     "GROUP BY DATE(b.createdAt) ORDER BY DATE(b.createdAt)")
       List<Object[]> findRevenueByDateRange(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 计算指定日期范围内的房间收入
        */
       @Query("SELECT COALESCE(SUM(b.finalAmount), 0) FROM Booking b WHERE " +
                     "b.paymentStatus = 'PAID' AND " +
                     "DATE(b.createdAt) BETWEEN :startDate AND :endDate")
       BigDecimal calculateRoomRevenueByDateRange(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 统计指定日期范围内的预订数量
        */
       @Query("SELECT COUNT(b) FROM Booking b WHERE " +
                     "DATE(b.createdAt) BETWEEN :startDate AND :endDate")
       Long countBookingsByDateRange(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 按酒店统计收入排行
        */
       @Query("SELECT b.hotel.id, b.hotel.name, COALESCE(SUM(b.finalAmount), 0) as revenue, COUNT(b) as bookingCount " +
                     "FROM Booking b WHERE b.paymentStatus = 'PAID' AND " +
                     "DATE(b.createdAt) BETWEEN :startDate AND :endDate " +
                     "GROUP BY b.hotel.id, b.hotel.name " +
                     "ORDER BY revenue DESC")
       List<Object[]> findHotelRevenueRanking(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 按预订状态统计
        */
       @Query("SELECT b.status, COUNT(b) as count, COALESCE(SUM(b.finalAmount), 0) as revenue " +
                     "FROM Booking b WHERE DATE(b.createdAt) BETWEEN :startDate AND :endDate " +
                     "GROUP BY b.status")
       List<Object[]> findBookingStatusDistribution(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 按房型统计预订情况
        */
       @Query("SELECT b.roomType.id, b.roomType.name, COUNT(b) as bookingCount, " +
                     "COALESCE(SUM(b.finalAmount), 0) as revenue " +
                     "FROM Booking b WHERE b.paymentStatus = 'PAID' AND " +
                     "DATE(b.createdAt) BETWEEN :startDate AND :endDate " +
                     "GROUP BY b.roomType.id, b.roomType.name " +
                     "ORDER BY bookingCount DESC")
       List<Object[]> findRoomTypeBookingStats(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 按月统计预订趋势
        */
       @Query("SELECT YEAR(b.createdAt) as year, MONTH(b.createdAt) as month, " +
                     "COUNT(b) as bookingCount, COALESCE(SUM(b.finalAmount), 0) as revenue " +
                     "FROM Booking b WHERE b.paymentStatus = 'PAID' AND " +
                     "DATE(b.createdAt) BETWEEN :startDate AND :endDate " +
                     "GROUP BY YEAR(b.createdAt), MONTH(b.createdAt) " +
                     "ORDER BY year, month")
       List<Object[]> findMonthlyBookingTrends(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 计算取消率
        */
       @Query("SELECT " +
                     "(SELECT COUNT(b1) FROM Booking b1 WHERE b1.status = 'CANCELLED' AND DATE(b1.createdAt) BETWEEN :startDate AND :endDate) * 100.0 / "
                     +
                     "(SELECT COUNT(b2) FROM Booking b2 WHERE DATE(b2.createdAt) BETWEEN :startDate AND :endDate) " +
                     "as cancellationRate")
       Double calculateCancellationRate(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 按创建时间范围统计预订数量
        */
       @Query("SELECT COUNT(b) FROM Booking b WHERE b.createdAt BETWEEN :startDateTime AND :endDateTime")
       int countByCreatedAtBetween(@Param("startDateTime") LocalDateTime startDateTime,
                     @Param("endDateTime") LocalDateTime endDateTime);

       /**
        * 按创建时间范围统计总金额
        */
       @Query("SELECT COALESCE(SUM(b.finalAmount), 0) FROM Booking b WHERE " +
                     "b.paymentStatus = 'PAID' AND " +
                     "b.createdAt BETWEEN :startDateTime AND :endDateTime")
       BigDecimal sumTotalAmountByCreatedAtBetween(@Param("startDateTime") LocalDateTime startDateTime,
                     @Param("endDateTime") LocalDateTime endDateTime);

       /**
        * 获取收入汇总数据
        */
       @Query("SELECT COALESCE(SUM(b.finalAmount), 0) as totalRevenue, " +
                     "COUNT(b) as totalBookings, " +
                     "COALESCE(AVG(b.finalAmount), 0) as avgBookingValue " +
                     "FROM Booking b WHERE b.paymentStatus = 'PAID' AND " +
                     "DATE(b.createdAt) BETWEEN :startDate AND :endDate")
       Object[] findRevenueSummaryByDateRange(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 按收入分类查询
        */
       @Query("SELECT '客房收入' as category, COALESCE(SUM(b.finalAmount), 0) as amount, COUNT(b) as count " +
                     "FROM Booking b WHERE b.paymentStatus = 'PAID' AND " +
                     "DATE(b.createdAt) BETWEEN :startDate AND :endDate")
       List<Object[]> findRevenueByCategoryAndDateRange(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 按酒店和日期范围查询收入
        */
       @Query("SELECT b.hotel.id, b.hotel.name, COALESCE(SUM(b.finalAmount), 0) as revenue, COUNT(b) as bookingCount " +
                     "FROM Booking b WHERE b.paymentStatus = 'PAID' AND " +
                     "DATE(b.createdAt) BETWEEN :startDate AND :endDate " +
                     "GROUP BY b.hotel.id, b.hotel.name " +
                     "ORDER BY revenue DESC")
       List<Object[]> findRevenueByHotelAndDateRange(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       /**
        * 报表查询：根据条件查找预订
        */
       @Query("SELECT b FROM Booking b " +
                     "LEFT JOIN FETCH b.user " +
                     "LEFT JOIN FETCH b.hotel " +
                     "LEFT JOIN FETCH b.room " +
                     "LEFT JOIN FETCH b.room.roomType " +
                     "LEFT JOIN FETCH b.room.hotel " +
                     "WHERE (:startDate IS NULL OR DATE(b.createdAt) >= :startDate) " +
                     "AND (:endDate IS NULL OR DATE(b.createdAt) <= :endDate) " +
                     "AND (:hotelId IS NULL OR b.hotel.id = :hotelId) " +
                     "AND (:status IS NULL OR b.status = :status)")
       Page<Booking> findBookingsForReport(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate,
                     @Param("hotelId") String hotelId,
                     @Param("status") String status,
                     Pageable pageable);

       /**
        * 根据用户ID查找预订
        */
       List<Booking> findByUserId(Long userId);

       // ==================== 仪表板相关查询方法 ====================

       /**
        * 根据创建时间范围查找预订
        */
       List<Booking> findByCreatedAtBetween(LocalDateTime startDateTime, LocalDateTime endDateTime);

       /**
        * 根据创建时间和支付状态查找预订
        */
       List<Booking> findByCreatedAtAfterAndPaymentStatus(LocalDateTime createdAt, Booking.PaymentStatus paymentStatus);

       // ==================== 收入占比统计查询方法 ====================

       /**
        * 按酒店统计收入（仅统计已支付订单）
        */
       @Query("SELECT b.hotel.id, b.hotel.name, COALESCE(SUM(b.finalAmount), 0) as revenue " +
                     "FROM Booking b WHERE b.paymentStatus = 'PAID' " +
                     "GROUP BY b.hotel.id, b.hotel.name " +
                     "ORDER BY revenue DESC")
       List<Object[]> findRevenueByHotel();

       /**
        * 计算酒店预订总收入（仅统计已支付订单）
        */
       @Query("SELECT COALESCE(SUM(b.finalAmount), 0) FROM Booking b WHERE b.paymentStatus = 'PAID'")
       BigDecimal calculateTotalHotelRevenue();

}
