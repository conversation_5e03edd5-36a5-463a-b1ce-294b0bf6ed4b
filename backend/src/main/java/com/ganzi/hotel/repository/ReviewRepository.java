package com.ganzi.hotel.repository;

import com.ganzi.hotel.entity.Review;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 评价数据访问层
 */
@Repository
public interface ReviewRepository extends JpaRepository<Review, Long> {

       /**
        * 根据酒店ID查找已通过的评价
        */
       List<Review> findByHotelIdAndStatusOrderByCreatedAtDesc(Long hotelId, Review.ReviewStatus status);

       /**
        * 根据酒店ID分页查找已通过的评价
        */
       Page<Review> findByHotelIdAndStatusOrderByCreatedAtDesc(Long hotelId, Review.ReviewStatus status,
                     Pageable pageable);

       /**
        * 根据用户ID查找评价
        */
       List<Review> findByUserIdOrderByCreatedAtDesc(Long userId);

       /**
        * 根据用户ID分页查找评价
        */
       Page<Review> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

       /**
        * 根据预订ID查找评价
        */
       Optional<Review> findByBookingId(Long bookingId);

       /**
        * 根据状态查找评价
        */
       List<Review> findByStatusOrderByCreatedAtDesc(Review.ReviewStatus status);

       /**
        * 根据状态分页查找评价
        */
       Page<Review> findByStatusOrderByCreatedAtDesc(Review.ReviewStatus status, Pageable pageable);

       /**
        * 检查用户是否已对酒店评价
        */
       boolean existsByUserIdAndHotelId(Long userId, Long hotelId);

       /**
        * 检查用户是否已对预订评价
        */
       boolean existsByUserIdAndBookingId(Long userId, Long bookingId);

       /**
        * 计算酒店平均评分
        */
       @Query("SELECT AVG(r.rating) FROM Review r WHERE r.hotel.id = :hotelId AND r.status = 'APPROVED'")
       Double calculateAverageRatingByHotel(@Param("hotelId") Long hotelId);

       /**
        * 计算酒店各项评分平均值
        */
       @Query("SELECT " +
                     "AVG(r.rating) as overallRating, " +
                     "AVG(r.serviceRating) as serviceRating, " +
                     "AVG(r.cleanlinessRating) as cleanlinessRating, " +
                     "AVG(r.locationRating) as locationRating, " +
                     "AVG(r.valueRating) as valueRating " +
                     "FROM Review r WHERE r.hotel.id = :hotelId AND r.status = 'APPROVED'")
       Object[] calculateDetailedRatingsByHotel(@Param("hotelId") Long hotelId);

       /**
        * 统计酒店评价数量
        */
       @Query("SELECT COUNT(r) FROM Review r WHERE r.hotel.id = :hotelId AND r.status = 'APPROVED'")
       long countApprovedReviewsByHotel(@Param("hotelId") Long hotelId);

       /**
        * 统计各评分等级的数量
        */
       @Query("SELECT r.rating, COUNT(r) FROM Review r WHERE r.hotel.id = :hotelId AND r.status = 'APPROVED' GROUP BY r.rating ORDER BY r.rating")
       List<Object[]> countReviewsByRating(@Param("hotelId") Long hotelId);

       /**
        * 查找指定时间范围内的评价
        */
       @Query("SELECT r FROM Review r WHERE r.createdAt BETWEEN :startTime AND :endTime ORDER BY r.createdAt DESC")
       List<Review> findReviewsInTimeRange(@Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime);

       /**
        * 查找指定时间范围内的评价（分页）
        */
       @Query("SELECT r FROM Review r WHERE r.createdAt BETWEEN :startTime AND :endTime ORDER BY r.createdAt DESC")
       Page<Review> findReviewsInTimeRange(@Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime,
                     Pageable pageable);

       /**
        * 查找最新的评价
        */
       @Query("SELECT r FROM Review r WHERE r.status = 'APPROVED' ORDER BY r.createdAt DESC")
       List<Review> findLatestApprovedReviews(Pageable pageable);

       /**
        * 查找高评分的评价
        */
       @Query("SELECT r FROM Review r WHERE r.hotel.id = :hotelId AND r.status = 'APPROVED' AND r.rating >= :minRating ORDER BY r.rating DESC, r.createdAt DESC")
       List<Review> findHighRatingReviews(@Param("hotelId") Long hotelId, @Param("minRating") Integer minRating);

       /**
        * 查找有评论内容的评价
        */
       @Query("SELECT r FROM Review r WHERE r.hotel.id = :hotelId AND r.status = 'APPROVED' AND r.comment IS NOT NULL AND LENGTH(r.comment) > 0 ORDER BY r.createdAt DESC")
       List<Review> findReviewsWithComments(@Param("hotelId") Long hotelId);

       /**
        * 查找有评论内容的评价（分页）
        */
       @Query("SELECT r FROM Review r WHERE r.hotel.id = :hotelId AND r.status = 'APPROVED' AND r.comment IS NOT NULL AND LENGTH(r.comment) > 0 ORDER BY r.createdAt DESC")
       Page<Review> findReviewsWithComments(@Param("hotelId") Long hotelId, Pageable pageable);

       /**
        * 复合查询评价
        */
       @Query("SELECT r FROM Review r WHERE " +
                     "(:hotelId IS NULL OR r.hotel.id = :hotelId) AND " +
                     "(:userId IS NULL OR r.user.id = :userId) AND " +
                     "(:status IS NULL OR r.status = :status) AND " +
                     "(:minRating IS NULL OR r.rating >= :minRating) AND " +
                     "(:maxRating IS NULL OR r.rating <= :maxRating) AND " +
                     "(:startTime IS NULL OR r.createdAt >= :startTime) AND " +
                     "(:endTime IS NULL OR r.createdAt <= :endTime) " +
                     "ORDER BY r.createdAt DESC")
       Page<Review> findReviewsWithFilters(@Param("hotelId") Long hotelId,
                     @Param("userId") Long userId,
                     @Param("status") Review.ReviewStatus status,
                     @Param("minRating") Integer minRating,
                     @Param("maxRating") Integer maxRating,
                     @Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime,
                     Pageable pageable);

       /**
        * 统计待审核的评价数量
        */
       @Query("SELECT COUNT(r) FROM Review r WHERE r.status = 'PENDING'")
       long countPendingReviews();

       /**
        * 查找需要管理员回复的评价
        */
       @Query("SELECT r FROM Review r WHERE r.status = 'APPROVED' AND r.adminReply IS NULL AND r.rating <= :lowRating ORDER BY r.createdAt")
       List<Review> findReviewsNeedingReply(@Param("lowRating") Integer lowRating);

       /**
        * 统计用户评价总数
        */
       @Query("SELECT COUNT(r) FROM Review r WHERE r.user.id = :userId")
       long countReviewsByUser(@Param("userId") Long userId);

       /**
        * 查找用户的最新评价
        */
       @Query("SELECT r FROM Review r WHERE r.user.id = :userId ORDER BY r.createdAt DESC")
       List<Review> findLatestReviewsByUser(@Param("userId") Long userId, Pageable pageable);

       /**
        * 按创建时间范围统计评价数量
        */
       @Query("SELECT COUNT(r) FROM Review r WHERE r.createdAt BETWEEN :startDateTime AND :endDateTime")
       int countByCreatedAtBetween(@Param("startDateTime") LocalDateTime startDateTime,
                     @Param("endDateTime") LocalDateTime endDateTime);

}
