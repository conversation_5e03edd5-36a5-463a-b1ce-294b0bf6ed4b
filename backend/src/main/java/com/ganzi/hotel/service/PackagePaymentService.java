package com.ganzi.hotel.service;

import com.ganzi.hotel.dto.PackagePaymentDto;
import com.ganzi.hotel.dto.PackagePaymentRequestDto;
import com.ganzi.hotel.dto.PackagePaymentResponseDto;
import com.ganzi.hotel.entity.PackageBooking;
import com.ganzi.hotel.entity.PackagePayment;
import com.ganzi.hotel.entity.User;
import com.ganzi.hotel.repository.PackageBookingRepository;
import com.ganzi.hotel.repository.PackagePaymentRepository;
import com.ganzi.hotel.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 套餐付款服务层
 */
@Service
@Transactional
public class PackagePaymentService {

    private static final Logger logger = LoggerFactory.getLogger(PackagePaymentService.class);

    @Autowired
    private PackagePaymentRepository paymentRepository;

    @Autowired
    private PackageBookingRepository bookingRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * 创建付款
     */
    public PackagePaymentResponseDto createPayment(PackagePaymentRequestDto requestDto, Long userId) {
        logger.info("创建套餐付款，请求: {}, 用户ID: {}", requestDto, userId);

        // 验证套餐预订
        PackageBooking booking = bookingRepository.findById(requestDto.getPackageBookingId())
                .orElseThrow(() -> new IllegalArgumentException("套餐预订不存在"));

        // 验证用户
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在"));

        // 验证预订状态
        if (booking.getStatus() != PackageBooking.BookingStatus.PENDING &&
                booking.getStatus() != PackageBooking.BookingStatus.CONFIRMED) {
            throw new IllegalArgumentException("只能为待确认或已确认的预订创建付款");
        }

        // 验证付款金额
        if (requestDto.getAmount().compareTo(booking.getTotalAmount()) > 0) {
            throw new IllegalArgumentException("付款金额不能超过预订总金额");
        }

        // 检查是否已有成功的付款
        BigDecimal paidAmount = paymentRepository.calculateTotalPaidAmountByBooking(booking.getId());
        BigDecimal remainingAmount = booking.getTotalAmount().subtract(paidAmount);

        if (requestDto.getAmount().compareTo(remainingAmount) > 0) {
            throw new IllegalArgumentException("付款金额超过剩余应付金额");
        }

        // 检查重复付款
        LocalDateTime timeThreshold = LocalDateTime.now().minusMinutes(5);
        if (paymentRepository.existsDuplicatePayment(
                booking.getId(), userId, requestDto.getAmount(), timeThreshold)) {
            throw new IllegalArgumentException("检测到重复付款，请稍后再试");
        }

        // 创建付款记录
        PackagePayment payment = new PackagePayment();
        payment.setPaymentNumber(generatePaymentNumber());
        payment.setPackageBooking(booking);
        payment.setUser(user);
        payment.setAmount(requestDto.getAmount());
        payment.setCurrency(requestDto.getCurrency());
        payment.setPaymentMethod(requestDto.getPaymentMethod());
        payment.setStatus(PackagePayment.PaymentStatus.PENDING);
        payment.setClientIp(requestDto.getClientIp());
        payment.setUserAgent(requestDto.getUserAgent());
        payment.setCreatedBy(userId);

        // 设置过期时间
        if (requestDto.getExpiresAt() != null) {
            payment.setExpiresAt(requestDto.getExpiresAt());
        } else {
            payment.setExpiresAt(LocalDateTime.now().plusMinutes(30));
        }

        // 保存付款记录
        PackagePayment savedPayment = paymentRepository.save(payment);
        logger.info("套餐付款记录创建成功，付款号: {}", savedPayment.getPaymentNumber());

        // 根据付款方式初始化支付网关
        PackagePaymentResponseDto response = initializePaymentGateway(savedPayment, requestDto);

        return response;
    }

    /**
     * 处理付款
     */
    public PackagePaymentDto processPayment(String paymentNumber) {
        logger.info("处理套餐付款，付款号: {}", paymentNumber);

        PackagePayment payment = paymentRepository.findByPaymentNumber(paymentNumber)
                .orElseThrow(() -> new IllegalArgumentException("付款记录不存在"));

        if (!payment.isPending()) {
            throw new IllegalArgumentException("只能处理待付款状态的记录");
        }

        if (payment.isExpired()) {
            payment.setStatus(PackagePayment.PaymentStatus.CANCELLED);
            payment.setFailureReason("付款已过期");
            paymentRepository.save(payment);
            throw new IllegalArgumentException("付款已过期");
        }

        // 更新状态为处理中
        payment.setStatus(PackagePayment.PaymentStatus.PROCESSING);
        paymentRepository.save(payment);

        // 调用支付网关处理付款
        boolean paymentSuccess = processPaymentGateway(payment);

        if (paymentSuccess) {
            // 付款成功
            payment.setStatus(PackagePayment.PaymentStatus.SUCCESS);
            payment.setTransactionId(generateTransactionId());
            payment.setProcessedAt(LocalDateTime.now());
            payment.setGatewayResponse("付款成功");

            // 更新预订付款状态
            updateBookingPaymentStatus(payment.getPackageBooking());
        } else {
            // 付款失败
            payment.setStatus(PackagePayment.PaymentStatus.FAILED);
            payment.setFailureReason("支付网关处理失败");
            payment.setGatewayResponse("付款失败");
            payment.incrementRetryCount();
        }

        PackagePayment savedPayment = paymentRepository.save(payment);
        logger.info("套餐付款处理完成，付款号: {}, 状态: {}", paymentNumber, savedPayment.getStatus());

        return convertToDto(savedPayment);
    }

    /**
     * 查询付款记录
     */
    @Transactional(readOnly = true)
    public Optional<PackagePaymentDto> getPaymentByNumber(String paymentNumber) {
        return paymentRepository.findByPaymentNumber(paymentNumber)
                .map(this::convertToDto);
    }

    /**
     * 获取预订的付款记录
     */
    @Transactional(readOnly = true)
    public List<PackagePaymentDto> getPaymentsByBooking(Long packageBookingId) {
        List<PackagePayment> payments = paymentRepository.findByPackageBookingIdOrderByCreatedAtDesc(packageBookingId);
        return payments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取用户的付款记录
     */
    @Transactional(readOnly = true)
    public Page<PackagePaymentDto> getUserPayments(Long userId, Pageable pageable) {
        Page<PackagePayment> payments = paymentRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        return payments.map(this::convertToDto);
    }

    /**
     * 搜索付款记录
     */
    @Transactional(readOnly = true)
    public Page<PackagePaymentDto> searchPayments(Long userId, Long packageBookingId,
            PackagePayment.PaymentStatus status,
            PackagePayment.PaymentMethod paymentMethod,
            LocalDateTime startTime, LocalDateTime endTime,
            Pageable pageable) {
        Page<PackagePayment> payments = paymentRepository.searchPayments(
                userId, packageBookingId, status, paymentMethod, startTime, endTime, pageable);
        return payments.map(this::convertToDto);
    }

    /**
     * 取消付款
     */
    public PackagePaymentDto cancelPayment(String paymentNumber, String reason) {
        logger.info("取消套餐付款，付款号: {}, 原因: {}", paymentNumber, reason);

        PackagePayment payment = paymentRepository.findByPaymentNumber(paymentNumber)
                .orElseThrow(() -> new IllegalArgumentException("付款记录不存在"));

        if (!payment.isPending() && !payment.isProcessing()) {
            throw new IllegalArgumentException("只能取消待付款或处理中的记录");
        }

        payment.setStatus(PackagePayment.PaymentStatus.CANCELLED);
        payment.setFailureReason(reason);
        payment.setUpdatedBy(payment.getUser().getId());

        PackagePayment savedPayment = paymentRepository.save(payment);
        logger.info("套餐付款取消成功，付款号: {}", paymentNumber);

        return convertToDto(savedPayment);
    }

    /**
     * 申请退款
     */
    public PackagePaymentDto requestRefund(String paymentNumber, BigDecimal refundAmount, String reason) {
        logger.info("申请套餐付款退款，付款号: {}, 退款金额: {}, 原因: {}", paymentNumber, refundAmount, reason);

        PackagePayment payment = paymentRepository.findByPaymentNumber(paymentNumber)
                .orElseThrow(() -> new IllegalArgumentException("付款记录不存在"));

        if (!payment.isSuccess()) {
            throw new IllegalArgumentException("只能对成功的付款申请退款");
        }

        BigDecimal maxRefundAmount = payment.getAmount().subtract(payment.getRefundAmount());
        if (refundAmount.compareTo(maxRefundAmount) > 0) {
            throw new IllegalArgumentException("退款金额不能超过可退款金额");
        }

        // 处理退款逻辑
        boolean refundSuccess = processRefund(payment, refundAmount, reason);

        if (refundSuccess) {
            payment.setRefundAmount(payment.getRefundAmount().add(refundAmount));
            payment.setRefundReason(reason);
            payment.setRefundAt(LocalDateTime.now());

            // 判断是否全额退款
            if (payment.getRefundAmount().compareTo(payment.getAmount()) >= 0) {
                payment.setStatus(PackagePayment.PaymentStatus.REFUNDED);
            } else {
                payment.setStatus(PackagePayment.PaymentStatus.PARTIAL_REFUND);
            }

            // 更新预订付款状态
            updateBookingPaymentStatus(payment.getPackageBooking());
        }

        PackagePayment savedPayment = paymentRepository.save(payment);
        logger.info("套餐付款退款处理完成，付款号: {}, 退款状态: {}", paymentNumber, savedPayment.getStatus());

        return convertToDto(savedPayment);
    }

    /**
     * 获取付款统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getPaymentStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 今日统计
        long todayCount = paymentRepository.countTodayPayments();
        BigDecimal todayRevenue = paymentRepository.calculateTodayRevenue();

        statistics.put("todayPaymentCount", todayCount);
        statistics.put("todayRevenue", todayRevenue);

        // 状态统计
        LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        LocalDateTime endOfMonth = LocalDateTime.now();
        List<Object[]> statusCounts = paymentRepository.countPaymentsByStatusInTimeRange(startOfMonth, endOfMonth);

        Map<String, Long> statusStatistics = new HashMap<>();
        for (Object[] row : statusCounts) {
            PackagePayment.PaymentStatus status = (PackagePayment.PaymentStatus) row[0];
            Long count = (Long) row[1];
            statusStatistics.put(status.name(), count);
        }
        statistics.put("statusStatistics", statusStatistics);

        // 付款方式统计
        List<Object[]> methodRevenues = paymentRepository.calculateRevenueByPaymentMethodInTimeRange(startOfMonth,
                endOfMonth);
        Map<String, BigDecimal> methodStatistics = new HashMap<>();
        for (Object[] row : methodRevenues) {
            PackagePayment.PaymentMethod method = (PackagePayment.PaymentMethod) row[0];
            BigDecimal revenue = (BigDecimal) row[1];
            methodStatistics.put(method.name(), revenue);
        }
        statistics.put("paymentMethodStatistics", methodStatistics);

        return statistics;
    }

    // 私有辅助方法

    /**
     * 生成付款编号
     */
    private String generatePaymentNumber() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String random = String.valueOf((int) (Math.random() * 10000));
        return "PP" + timestamp + String.format("%04d", Integer.parseInt(random));
    }

    /**
     * 生成交易ID
     */
    private String generateTransactionId() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        String random = String.valueOf((int) (Math.random() * 1000));
        return "TXN" + timestamp + String.format("%03d", Integer.parseInt(random));
    }

    /**
     * 初始化支付网关
     */
    private PackagePaymentResponseDto initializePaymentGateway(PackagePayment payment,
            PackagePaymentRequestDto requestDto) {
        PackagePaymentResponseDto response = new PackagePaymentResponseDto();

        // 基本信息
        response.setId(payment.getId());
        response.setPaymentNumber(payment.getPaymentNumber());
        response.setPackageBookingId(payment.getPackageBooking().getId());
        response.setPackageBookingNumber(payment.getPackageBooking().getBookingNumber());
        response.setPackageName(payment.getPackageBooking().getCulturalPackage().getName());
        response.setAmount(payment.getAmount());
        response.setCurrency(payment.getCurrency());
        response.setPaymentMethod(payment.getPaymentMethod());
        response.setStatus(payment.getStatus());
        response.setStatusDescription(payment.getStatus().getDescription());
        response.setPaymentMethodDescription(payment.getPaymentMethod().getDescription());
        response.setExpiresAt(payment.getExpiresAt());
        response.setCreatedAt(payment.getCreatedAt());

        // 根据付款方式设置不同的响应信息
        switch (payment.getPaymentMethod()) {
            case ALIPAY:
                response.setGatewayType("ALIPAY");
                response.setNeedRedirect(true);
                response.setPaymentUrl("https://openapi.alipay.com/gateway.do?...");
                response.setMessage("请跳转到支付宝完成付款");
                response.setNextAction("REDIRECT");
                response.setInstructions(new String[] { "点击确认后将跳转到支付宝", "请在30分钟内完成付款" });
                break;

            case WECHAT_PAY:
                response.setGatewayType("WECHAT");
                response.setNeedQrCode(true);
                response.setQrCodeUrl("https://api.mch.weixin.qq.com/pay/qrcode?...");
                response.setMessage("请使用微信扫码付款");
                response.setNextAction("SCAN_QR");
                response.setInstructions(new String[] { "打开微信扫一扫", "扫描二维码完成付款" });
                break;

            case CREDIT_CARD:
            case BANK_CARD:
                response.setGatewayType("CARD");
                response.setNeedRedirect(true);
                response.setPaymentUrl("/payment/card/" + payment.getPaymentNumber());
                response.setMessage("请填写银行卡信息");
                response.setNextAction("FILL_CARD");
                response.setInstructions(new String[] { "填写银行卡号和相关信息", "确认付款信息后提交" });
                break;

            default:
                response.setMessage("付款方式暂不支持在线付款");
                response.setNextAction("CONTACT_SUPPORT");
        }

        response.setExpired(payment.isExpired());
        response.setCanRetry(payment.canRetry());

        return response;
    }

    /**
     * 处理支付网关付款（模拟）
     */
    private boolean processPaymentGateway(PackagePayment payment) {
        // 这里应该调用实际的支付网关API
        // 目前返回模拟结果
        return Math.random() > 0.1; // 90%成功率
    }

    /**
     * 处理退款（模拟）
     */
    private boolean processRefund(PackagePayment payment, BigDecimal refundAmount, String reason) {
        // 这里应该调用实际的退款API
        // 目前返回模拟结果
        return Math.random() > 0.05; // 95%成功率
    }

    /**
     * 更新预订付款状态
     */
    private void updateBookingPaymentStatus(PackageBooking booking) {
        BigDecimal totalPaid = paymentRepository.calculateTotalPaidAmountByBooking(booking.getId());

        if (totalPaid.compareTo(BigDecimal.ZERO) == 0) {
            booking.setPaymentStatus(PackageBooking.PaymentStatus.PENDING);
        } else if (totalPaid.compareTo(booking.getTotalAmount()) >= 0) {
            booking.setPaymentStatus(PackageBooking.PaymentStatus.PAID);
        } else {
            booking.setPaymentStatus(PackageBooking.PaymentStatus.PARTIAL);
        }

        booking.setPaidAmount(totalPaid);
        booking.setLastPaymentAt(LocalDateTime.now());
        bookingRepository.save(booking);
    }

    /**
     * 转换为DTO
     */
    private PackagePaymentDto convertToDto(PackagePayment payment) {
        PackagePaymentDto dto = new PackagePaymentDto();

        dto.setId(payment.getId());
        dto.setPaymentNumber(payment.getPaymentNumber());
        dto.setPackageBookingId(payment.getPackageBooking().getId());
        dto.setPackageBookingNumber(payment.getPackageBooking().getBookingNumber());
        dto.setPackageName(payment.getPackageBooking().getCulturalPackage().getName());
        dto.setUserId(payment.getUser().getId());
        dto.setUserName(payment.getUser().getFullName());
        dto.setAmount(payment.getAmount());
        dto.setCurrency(payment.getCurrency());
        dto.setPaymentMethod(payment.getPaymentMethod());
        dto.setStatus(payment.getStatus());
        dto.setGatewayType(payment.getGatewayType());
        dto.setTransactionId(payment.getTransactionId());
        dto.setGatewayOrderId(payment.getGatewayOrderId());
        dto.setGatewayResponse(payment.getGatewayResponse());
        dto.setProcessedAt(payment.getProcessedAt());
        dto.setFailureReason(payment.getFailureReason());
        dto.setRefundReason(payment.getRefundReason());
        dto.setRefundAmount(payment.getRefundAmount());
        dto.setRefundAt(payment.getRefundAt());
        dto.setClientIp(payment.getClientIp());
        dto.setUserAgent(payment.getUserAgent());
        dto.setPaymentToken(payment.getPaymentToken());
        dto.setExpiresAt(payment.getExpiresAt());
        dto.setRetryCount(payment.getRetryCount());
        dto.setMaxRetryCount(payment.getMaxRetryCount());
        dto.setCreatedAt(payment.getCreatedAt());
        dto.setUpdatedAt(payment.getUpdatedAt());
        dto.setCreatedBy(payment.getCreatedBy());
        dto.setUpdatedBy(payment.getUpdatedBy());

        // 扩展字段
        dto.setExpired(payment.isExpired());
        dto.setCanRetry(payment.canRetry());
        dto.setStatusDescription(payment.getStatus().getDescription());
        dto.setPaymentMethodDescription(payment.getPaymentMethod().getDescription());

        return dto;
    }

    /**
     * 模拟支付成功
     */
    public PackagePaymentDto simulatePaymentSuccess(String paymentNumber) {
        logger.info("模拟支付成功，付款号: {}", paymentNumber);

        PackagePayment payment = paymentRepository.findByPaymentNumber(paymentNumber)
                .orElseThrow(() -> new IllegalArgumentException("付款记录不存在"));

        if (payment.getStatus() != PackagePayment.PaymentStatus.PENDING) {
            throw new IllegalArgumentException("只能对待付款状态的订单进行模拟支付");
        }

        // 更新支付状态为成功
        payment.setStatus(PackagePayment.PaymentStatus.SUCCESS);
        payment.setProcessedAt(LocalDateTime.now());
        payment.setGatewayOrderId("SIMULATE_" + System.currentTimeMillis());
        payment.setTransactionId("TXN_SIMULATE_" + System.currentTimeMillis());
        payment.setUpdatedAt(LocalDateTime.now());

        // 保存支付记录
        payment = paymentRepository.save(payment);

        // 更新预订状态
        PackageBooking booking = payment.getPackageBooking();
        if (booking != null) {
            // 计算已支付金额
            BigDecimal paidAmount = paymentRepository.calculateTotalPaidAmountByBooking(booking.getId());
            booking.setPaidAmount(paidAmount);
            booking.setLastPaymentAt(LocalDateTime.now());

            // 如果已支付金额大于等于总金额，更新支付状态为已支付
            if (paidAmount.compareTo(booking.getTotalAmount()) >= 0) {
                booking.setPaymentStatus(PackageBooking.PaymentStatus.PAID);
                booking.setStatus(PackageBooking.BookingStatus.CONFIRMED);
                booking.setConfirmedAt(LocalDateTime.now());
            } else {
                booking.setPaymentStatus(PackageBooking.PaymentStatus.PARTIAL);
            }

            bookingRepository.save(booking);
            logger.info("预订状态已更新，预订ID: {}, 支付状态: {}, 预订状态: {}",
                    booking.getId(), booking.getPaymentStatus(), booking.getStatus());
        }

        logger.info("模拟支付成功完成，付款号: {}", paymentNumber);
        return convertToDto(payment);
    }

    /**
     * 模拟支付失败
     */
    public PackagePaymentDto simulatePaymentFailure(String paymentNumber, String reason) {
        logger.info("模拟支付失败，付款号: {}, 原因: {}", paymentNumber, reason);

        PackagePayment payment = paymentRepository.findByPaymentNumber(paymentNumber)
                .orElseThrow(() -> new IllegalArgumentException("付款记录不存在"));

        if (payment.getStatus() != PackagePayment.PaymentStatus.PENDING) {
            throw new IllegalArgumentException("只能对待付款状态的订单进行模拟支付");
        }

        // 更新支付状态为失败
        payment.setStatus(PackagePayment.PaymentStatus.FAILED);
        payment.setProcessedAt(LocalDateTime.now());
        payment.setFailureReason(reason);
        payment.setUpdatedAt(LocalDateTime.now());

        // 保存支付记录
        payment = paymentRepository.save(payment);

        logger.info("模拟支付失败完成，付款号: {}", paymentNumber);
        return convertToDto(payment);
    }
}
