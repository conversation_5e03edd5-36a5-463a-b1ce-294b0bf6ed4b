package com.ganzi.hotel.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 数据库优化服务
 * 负责数据库性能优化
 */
@Service
public class DatabaseOptimizationService {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseOptimizationService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 应用启动时执行数据库优化
     */
    @PostConstruct
    public void initializeDatabase() {
        try {
            logger.info("开始执行数据库优化...");
            createReportIndexes();
            logger.info("数据库优化完成");
        } catch (Exception e) {
            logger.error("数据库优化失败", e);
        }
    }

    /**
     * 创建基础查询索引
     */
    private void createReportIndexes() {
        logger.info("创建基础查询索引...");

        String[] indexSqls = {
                // 基础业务查询索引 - 使用正确的表名和字段名
                "CREATE INDEX IF NOT EXISTS idx_bookings_hotel_status ON bookings(hotel_id, status)",
                "CREATE INDEX IF NOT EXISTS idx_rooms_hotel_id ON rooms(hotel_id)",
                "CREATE INDEX IF NOT EXISTS idx_room_types_hotel_id ON room_types(hotel_id)",
                "CREATE INDEX IF NOT EXISTS idx_reviews_hotel_id ON reviews(hotel_id)",
                // 添加支付状态索引用于收入统计
                "CREATE INDEX IF NOT EXISTS idx_bookings_payment_status ON bookings(payment_status, created_at)"
        };

        for (String sql : indexSqls) {
            try {
                jdbcTemplate.execute(sql);
                logger.debug("执行索引创建SQL: {}", sql);
            } catch (Exception e) {
                logger.warn("索引创建失败: {}, 错误: {}", sql, e.getMessage());
            }
        }
    }

    /**
     * 检查数据库索引状态
     */
    public void checkIndexStatus() {
        logger.info("检查数据库索引状态...");

        String sql = """
                SELECT
                    TABLE_NAME,
                    INDEX_NAME,
                    COLUMN_NAME,
                    CARDINALITY
                FROM INFORMATION_SCHEMA.STATISTICS
                WHERE TABLE_SCHEMA = DATABASE()
                AND INDEX_NAME LIKE 'idx_%'
                ORDER BY TABLE_NAME, INDEX_NAME
                """;

        try {
            List<Map<String, Object>> indexes = jdbcTemplate.queryForList(sql);
            logger.info("当前数据库索引数量: {}", indexes.size());

            for (Map<String, Object> index : indexes) {
                logger.debug("索引: {}.{} - 列: {}, 基数: {}",
                        index.get("TABLE_NAME"),
                        index.get("INDEX_NAME"),
                        index.get("COLUMN_NAME"),
                        index.get("CARDINALITY"));
            }
        } catch (Exception e) {
            logger.error("检查索引状态失败", e);
        }
    }
}
