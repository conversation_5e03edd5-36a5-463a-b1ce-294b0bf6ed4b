package com.ganzi.hotel.service;

import com.ganzi.hotel.dto.HotelDto;
import com.ganzi.hotel.dto.HotelSearchDto;
import com.ganzi.hotel.dto.HotelStatisticsDto;
import com.ganzi.hotel.dto.HotelCreationRequestDto;
import com.ganzi.hotel.dto.RoomPreviewDto;
import com.ganzi.hotel.dto.RoomTypeConfigDto;
import com.ganzi.hotel.dto.RoomTypeDto;
import com.ganzi.hotel.entity.Hotel;
import com.ganzi.hotel.entity.Room;
import com.ganzi.hotel.entity.RoomType;
import com.ganzi.hotel.repository.BookingRepository;
import com.ganzi.hotel.repository.HotelRepository;
import com.ganzi.hotel.repository.ReviewRepository;
import com.ganzi.hotel.repository.RoomRepository;
import com.ganzi.hotel.repository.RoomTypeRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 酒店管理服务类
 */
@Service
@Transactional
public class HotelService {

    private static final Logger logger = LoggerFactory.getLogger(HotelService.class);

    @Autowired
    private HotelRepository hotelRepository;

    @Autowired
    private ReviewRepository reviewRepository;

    @Autowired
    private RoomRepository roomRepository;

    @Autowired
    private RoomTypeRepository roomTypeRepository;

    @Autowired
    private BookingRepository bookingRepository;

    @Autowired
    private RoomTypeService roomTypeService;

    /**
     * 获取所有活跃酒店
     */
    @Transactional(readOnly = true)
    public List<HotelDto> getAllActiveHotels() {
        logger.info("获取所有活跃酒店");
        List<Hotel> hotels = hotelRepository.findByIsActiveTrueOrderByName();
        return hotels.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID获取酒店详情
     */
    @Transactional(readOnly = true)
    public Optional<HotelDto> getHotelById(Long id) {
        logger.info("获取酒店详情，ID: {}", id);
        Optional<Hotel> hotel = hotelRepository.findById(id);
        return hotel.map(this::convertToDto);
    }

    /**
     * 搜索酒店
     */
    @Transactional(readOnly = true)
    public Page<HotelDto> searchHotels(HotelSearchDto searchDto) {
        logger.info("搜索酒店，条件: {}", searchDto);

        // 构建分页和排序
        Sort sort = buildSort(searchDto.getSortBy(), searchDto.getSortOrder());
        Pageable pageable = PageRequest.of(searchDto.getPage(), searchDto.getSize(), sort);

        // 执行搜索
        Page<Hotel> hotelPage = hotelRepository.searchHotelsWithFilters(
                searchDto.getCity(),
                searchDto.getStarRating(),
                searchDto.getKeyword(),
                pageable);

        // 转换为DTO并添加统计信息
        return hotelPage.map(this::convertToDto);
    }

    /**
     * 根据城市获取酒店
     */
    @Transactional(readOnly = true)
    public List<HotelDto> getHotelsByCity(String city) {
        logger.info("根据城市获取酒店: {}", city);
        List<Hotel> hotels = hotelRepository.findByCityAndIsActiveTrueOrderByName(city);
        return hotels.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 根据星级获取酒店
     */
    @Transactional(readOnly = true)
    public List<HotelDto> getHotelsByStarRating(Integer starRating) {
        logger.info("根据星级获取酒店: {}", starRating);
        List<Hotel> hotels = hotelRepository.findByStarRatingAndIsActiveTrueOrderByName(starRating);
        return hotels.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 创建酒店
     */
    public HotelDto createHotel(HotelDto hotelDto) {
        logger.info("创建酒店: {}", hotelDto.getName());

        Hotel hotel = convertToEntity(hotelDto);
        hotel.setId(null); // 确保是新建
        hotel.setIsActive(true);

        Hotel savedHotel = hotelRepository.save(hotel);
        logger.info("酒店创建成功，ID: {}", savedHotel.getId());

        return convertToDto(savedHotel);
    }

    /**
     * 更新酒店
     */
    public Optional<HotelDto> updateHotel(Long id, HotelDto hotelDto) {
        logger.info("更新酒店，ID: {}", id);

        Optional<Hotel> existingHotel = hotelRepository.findById(id);
        if (existingHotel.isEmpty()) {
            logger.warn("酒店不存在，ID: {}", id);
            return Optional.empty();
        }

        Hotel hotel = existingHotel.get();
        updateHotelFields(hotel, hotelDto);

        Hotel savedHotel = hotelRepository.save(hotel);
        logger.info("酒店更新成功，ID: {}", savedHotel.getId());

        return Optional.of(convertToDto(savedHotel));
    }

    /**
     * 删除酒店（软删除）
     */
    public boolean deleteHotel(Long id) {
        logger.info("删除酒店，ID: {}", id);

        Optional<Hotel> hotel = hotelRepository.findById(id);
        if (hotel.isEmpty()) {
            logger.warn("酒店不存在，ID: {}", id);
            return false;
        }

        // 检查是否有关联的预订记录
        long activeBookings = bookingRepository.countByHotelId(id);
        if (activeBookings > 0) {
            logger.warn("酒店有关联的预订记录，无法删除，ID: {}, 预订数: {}", id, activeBookings);
            throw new RuntimeException("该酒店有关联的预订记录，无法删除");
        }

        Hotel hotelEntity = hotel.get();
        hotelEntity.setIsActive(false);
        hotelRepository.save(hotelEntity);

        logger.info("酒店删除成功，ID: {}", id);
        return true;
    }

    /**
     * 获取所有城市列表
     */
    @Transactional(readOnly = true)
    public List<String> getAllCities() {
        logger.info("获取所有城市列表");
        return hotelRepository.findAllCities();
    }

    /**
     * 根据价格范围获取酒店
     */
    @Transactional(readOnly = true)
    public List<HotelDto> getHotelsByPriceRange(BigDecimal minPrice, BigDecimal maxPrice) {
        logger.info("根据价格范围获取酒店: {} - {}", minPrice, maxPrice);
        List<Hotel> hotels = hotelRepository.findHotelsByPriceRange(minPrice, maxPrice);
        return hotels.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取有房型的酒店
     */
    @Transactional(readOnly = true)
    public List<HotelDto> getHotelsWithRoomTypes() {
        logger.info("获取有房型的酒店");
        List<Hotel> hotels = hotelRepository.findHotelsWithActiveRoomTypes();
        return hotels.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取热门酒店
     */
    @Transactional(readOnly = true)
    public List<HotelDto> getFeaturedHotels(int limit) {
        logger.info("获取热门酒店，数量: {}", limit);

        // 获取所有活跃酒店，按星级和名称排序
        List<Hotel> hotels = hotelRepository.findByIsActiveTrueOrderByStarRatingDescNameAsc();

        // 限制数量
        return hotels.stream()
                .limit(limit)
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * 获取酒店统计信息
     */
    @Transactional(readOnly = true)
    public HotelStatisticsDto getHotelStatistics(Long hotelId) {
        logger.info("获取酒店统计信息，酒店ID: {}", hotelId);

        Hotel hotel = hotelRepository.findById(hotelId)
                .orElseThrow(() -> new IllegalArgumentException("酒店不存在"));

        HotelStatisticsDto statistics = new HotelStatisticsDto();
        statistics.setHotelId(hotelId);
        statistics.setHotelName(hotel.getName());

        // 房间统计
        long totalRooms = roomRepository.countByHotelId(hotelId);
        long availableRooms = roomRepository.countByHotelIdAndStatus(hotelId, Room.RoomStatus.AVAILABLE);
        long occupiedRooms = roomRepository.countByHotelIdAndStatus(hotelId, Room.RoomStatus.OCCUPIED);

        statistics.setTotalRooms(totalRooms);
        statistics.setAvailableRooms(availableRooms);
        statistics.setOccupiedRooms(occupiedRooms);
        statistics.setOccupancyRate(totalRooms > 0 ? (double) occupiedRooms / totalRooms * 100 : 0.0);

        return statistics;
    }

    /**
     * 生成房间预览列表
     */
    public List<RoomPreviewDto> generateRoomPreviews(Integer totalFloors, Integer roomsPerFloor) {
        logger.info("生成房间预览，楼层数: {}，每层房间数: {}", totalFloors, roomsPerFloor);

        if (totalFloors == null || totalFloors < 1 || totalFloors > 50) {
            throw new IllegalArgumentException("楼层数必须在1-50之间");
        }
        if (roomsPerFloor == null || roomsPerFloor < 1 || roomsPerFloor > 100) {
            throw new IllegalArgumentException("每层房间数必须在1-100之间");
        }

        List<RoomPreviewDto> roomPreviews = new ArrayList<>();

        for (int floor = 1; floor <= totalFloors; floor++) {
            for (int roomPos = 1; roomPos <= roomsPerFloor; roomPos++) {
                String roomNumber = generateRoomNumber(floor, roomPos);
                RoomPreviewDto preview = new RoomPreviewDto(roomNumber, floor, roomPos);
                roomPreviews.add(preview);
            }
        }

        logger.info("生成房间预览完成，总房间数: {}", roomPreviews.size());
        return roomPreviews;
    }

    /**
     * 创建自定义酒店
     */
    @Transactional
    public HotelDto createCustomHotel(HotelCreationRequestDto requestDto) {
        logger.info("开始创建自定义酒店: {}", requestDto.getName());

        try {
            // 1. 创建酒店基本信息
            Hotel hotel = createHotelFromRequest(requestDto);
            Hotel savedHotel = hotelRepository.save(hotel);
            logger.info("酒店基本信息创建成功，ID: {}", savedHotel.getId());

            // 2. 创建默认房型
            RoomType standardRoomType = createStandardRoomType(savedHotel, requestDto.getStandardRoomConfig());
            RoomType deluxeRoomType = createDeluxeRoomType(savedHotel, requestDto.getDeluxeRoomConfig());

            // 3. 批量创建房间
            List<Room> rooms = createRoomsForHotel(savedHotel, standardRoomType, deluxeRoomType,
                    requestDto.getTotalFloors(), requestDto.getRoomsPerFloor(), requestDto.getDeluxeRoomNumbers());

            logger.info("酒店房间创建完成，总房间数: {}", rooms.size());

            // 4. 返回创建结果
            return convertToDto(savedHotel);

        } catch (Exception e) {
            logger.error("创建自定义酒店失败: {}", requestDto.getName(), e);
            throw new RuntimeException("创建自定义酒店失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成房间编号
     */
    private String generateRoomNumber(int floor, int roomPosition) {
        // 格式：楼层号 + 房间序号（2位数）
        return String.format("%d%02d", floor, roomPosition);
    }

    /**
     * 从请求DTO创建酒店实体
     */
    private Hotel createHotelFromRequest(HotelCreationRequestDto requestDto) {
        Hotel hotel = new Hotel();
        hotel.setName(requestDto.getName());
        hotel.setDescription(requestDto.getDescription());
        hotel.setAddress(requestDto.getAddress());
        hotel.setCity(requestDto.getCity());
        hotel.setProvince(requestDto.getProvince());
        hotel.setPostalCode(requestDto.getPostalCode());
        hotel.setLatitude(requestDto.getLatitude());
        hotel.setLongitude(requestDto.getLongitude());
        hotel.setStarRating(requestDto.getStarRating());
        hotel.setPhone(requestDto.getPhone());
        hotel.setEmail(requestDto.getEmail());
        hotel.setWebsite(requestDto.getWebsite());
        hotel.setCheckInTime(requestDto.getCheckInTime());
        hotel.setCheckOutTime(requestDto.getCheckOutTime());
        hotel.setAmenities(requestDto.getAmenities());
        hotel.setImages(requestDto.getImages());
        hotel.setPolicies(requestDto.getPolicies());
        hotel.setIsActive(true);
        return hotel;
    }

    /**
     * 创建标准房型
     */
    private RoomType createStandardRoomType(Hotel hotel, RoomTypeConfigDto config) {
        if (config == null) {
            // 使用默认配置
            config = new RoomTypeConfigDto("标准间", new BigDecimal("200.00"), 2);
            config.setDescription("舒适的标准间，配备基础设施");
            config.setBedType("双人床");
            config.setRoomSize(new BigDecimal("25.0"));
        }

        RoomType roomType = new RoomType();
        roomType.setHotel(hotel);
        roomType.setName(config.getName());
        roomType.setDescription(config.getDescription());
        roomType.setBasePrice(config.getBasePrice());
        roomType.setMaxOccupancy(config.getMaxOccupancy());
        roomType.setBedType(config.getBedType());
        roomType.setRoomSize(config.getRoomSize());
        roomType.setAmenities(config.getAmenities());
        roomType.setImages(config.getImages());
        roomType.setIsActive(true);

        return roomTypeRepository.save(roomType);
    }

    /**
     * 创建豪华房型
     */
    private RoomType createDeluxeRoomType(Hotel hotel, RoomTypeConfigDto config) {
        if (config == null) {
            // 使用默认配置
            config = new RoomTypeConfigDto("豪华间", new BigDecimal("350.00"), 2);
            config.setDescription("豪华舒适的客房，配备高端设施");
            config.setBedType("大床");
            config.setRoomSize(new BigDecimal("35.0"));
        }

        RoomType roomType = new RoomType();
        roomType.setHotel(hotel);
        roomType.setName(config.getName());
        roomType.setDescription(config.getDescription());
        roomType.setBasePrice(config.getBasePrice());
        roomType.setMaxOccupancy(config.getMaxOccupancy());
        roomType.setBedType(config.getBedType());
        roomType.setRoomSize(config.getRoomSize());
        roomType.setAmenities(config.getAmenities());
        roomType.setImages(config.getImages());
        roomType.setIsActive(true);

        return roomTypeRepository.save(roomType);
    }

    /**
     * 为酒店批量创建房间
     */
    private List<Room> createRoomsForHotel(Hotel hotel, RoomType standardRoomType, RoomType deluxeRoomType,
            Integer totalFloors, Integer roomsPerFloor, List<String> deluxeRoomNumbers) {

        List<Room> rooms = new ArrayList<>();
        Set<String> deluxeRoomSet = deluxeRoomNumbers != null ? new HashSet<>(deluxeRoomNumbers) : new HashSet<>();

        for (int floor = 1; floor <= totalFloors; floor++) {
            for (int roomPos = 1; roomPos <= roomsPerFloor; roomPos++) {
                String roomNumber = generateRoomNumber(floor, roomPos);

                // 根据是否在豪华间列表中决定房型
                RoomType roomType = deluxeRoomSet.contains(roomNumber) ? deluxeRoomType : standardRoomType;

                Room room = new Room();
                room.setHotel(hotel);
                room.setRoomType(roomType);
                room.setRoomNumber(roomNumber);
                room.setFloorNumber(floor);
                room.setRoomPosition(roomPos);
                room.setStatus(Room.RoomStatus.AVAILABLE);
                room.setBuilding("A"); // 默认A栋

                rooms.add(room);
            }
        }

        // 批量保存房间
        return roomRepository.saveAll(rooms);
    }

    /**
     * 批量更新酒店状态
     */
    public List<HotelDto> batchUpdateHotelStatus(List<Long> hotelIds, boolean isActive) {
        logger.info("批量更新酒店状态，酒店数量: {}，状态: {}", hotelIds.size(), isActive);

        List<Hotel> hotels = hotelRepository.findAllById(hotelIds);
        hotels.forEach(hotel -> hotel.setIsActive(isActive));

        List<Hotel> savedHotels = hotelRepository.saveAll(hotels);
        return savedHotels.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    /**
     * 转换实体为DTO
     */
    private HotelDto convertToDto(Hotel hotel) {
        HotelDto dto = HotelDto.fromEntity(hotel);

        // 添加统计信息
        Optional<BigDecimal> minPrice = hotelRepository.findMinPriceByHotelId(hotel.getId());
        Optional<BigDecimal> maxPrice = hotelRepository.findMaxPriceByHotelId(hotel.getId());

        dto.setMinPrice(minPrice.orElse(null));
        dto.setMaxPrice(maxPrice.orElse(null));

        // 添加评分统计信息
        try {
            Double averageRating = reviewRepository.calculateAverageRatingByHotel(hotel.getId());
            long reviewCount = reviewRepository.countApprovedReviewsByHotel(hotel.getId());

            dto.setAverageRating(averageRating);
            dto.setReviewCount((int) reviewCount);
        } catch (Exception e) {
            logger.warn("获取酒店评分统计失败，酒店ID: {}", hotel.getId(), e);
            dto.setAverageRating(null);
            dto.setReviewCount(0);
        }

        // 添加管理后台需要的统计信息
        try {
            // 计算总房间数和可用房间数
            long totalRooms = roomRepository.countByHotelId(hotel.getId());
            long availableRooms = roomRepository.countByHotelIdAndStatus(
                    hotel.getId(), com.ganzi.hotel.entity.Room.RoomStatus.AVAILABLE);

            dto.setTotalRooms((int) totalRooms);
            dto.setAvailableRooms((int) availableRooms);

            // 计算入住率
            if (totalRooms > 0) {
                double occupancyRate = (double) (totalRooms - availableRooms) / totalRooms;
                dto.setOccupancyRate(occupancyRate);
            } else {
                dto.setOccupancyRate(0.0);
            }

            // 计算总预订数（使用BookingRepository统计）
            if (bookingRepository != null) {
                long totalBookings = bookingRepository.countByHotelId(hotel.getId());
                dto.setTotalBookings((int) totalBookings);
            } else {
                dto.setTotalBookings(0);
            }

        } catch (Exception e) {
            logger.warn("获取酒店房间统计失败，酒店ID: {}", hotel.getId(), e);
            dto.setTotalRooms(0);
            dto.setAvailableRooms(0);
            dto.setOccupancyRate(0.0);
            dto.setTotalBookings(0);
        }

        // 添加房间类型信息
        try {
            List<RoomTypeDto> roomTypes = roomTypeService.getRoomTypesByHotelId(hotel.getId());
            dto.setRoomTypes(roomTypes);
            dto.setRoomTypeCount(roomTypes.size());
        } catch (Exception e) {
            logger.warn("获取酒店房间类型失败，酒店ID: {}", hotel.getId(), e);
            dto.setRoomTypes(new java.util.ArrayList<>());
            dto.setRoomTypeCount(0);
        }

        return dto;
    }

    /**
     * 转换DTO为实体
     */
    private Hotel convertToEntity(HotelDto dto) {
        Hotel hotel = new Hotel();
        hotel.setId(dto.getId());
        hotel.setName(dto.getName());
        hotel.setDescription(dto.getDescription());
        hotel.setAddress(dto.getAddress());
        hotel.setCity(dto.getCity());
        hotel.setProvince(dto.getProvince());
        hotel.setPostalCode(dto.getPostalCode());
        hotel.setLatitude(dto.getLatitude());
        hotel.setLongitude(dto.getLongitude());
        hotel.setStarRating(dto.getStarRating());
        hotel.setPhone(dto.getPhone());
        hotel.setEmail(dto.getEmail());
        hotel.setWebsite(dto.getWebsite());
        hotel.setCheckInTime(dto.getCheckInTime());
        hotel.setCheckOutTime(dto.getCheckOutTime());
        hotel.setAmenities(dto.getAmenities());
        hotel.setImages(dto.getImages());
        hotel.setPolicies(dto.getPolicies());
        hotel.setIsActive(dto.getIsActive());
        return hotel;
    }

    /**
     * 更新酒店字段
     */
    private void updateHotelFields(Hotel hotel, HotelDto dto) {
        if (dto.getName() != null)
            hotel.setName(dto.getName());
        if (dto.getDescription() != null)
            hotel.setDescription(dto.getDescription());
        if (dto.getAddress() != null)
            hotel.setAddress(dto.getAddress());
        if (dto.getCity() != null)
            hotel.setCity(dto.getCity());
        if (dto.getProvince() != null)
            hotel.setProvince(dto.getProvince());
        if (dto.getPostalCode() != null)
            hotel.setPostalCode(dto.getPostalCode());
        if (dto.getLatitude() != null)
            hotel.setLatitude(dto.getLatitude());
        if (dto.getLongitude() != null)
            hotel.setLongitude(dto.getLongitude());
        if (dto.getStarRating() != null)
            hotel.setStarRating(dto.getStarRating());
        if (dto.getPhone() != null)
            hotel.setPhone(dto.getPhone());
        if (dto.getEmail() != null)
            hotel.setEmail(dto.getEmail());
        if (dto.getWebsite() != null)
            hotel.setWebsite(dto.getWebsite());
        if (dto.getCheckInTime() != null)
            hotel.setCheckInTime(dto.getCheckInTime());
        if (dto.getCheckOutTime() != null)
            hotel.setCheckOutTime(dto.getCheckOutTime());
        if (dto.getAmenities() != null)
            hotel.setAmenities(dto.getAmenities());
        if (dto.getImages() != null)
            hotel.setImages(dto.getImages());
        if (dto.getPolicies() != null)
            hotel.setPolicies(dto.getPolicies());
        if (dto.getIsActive() != null)
            hotel.setIsActive(dto.getIsActive());
    }

    /**
     * 构建排序
     */
    private Sort buildSort(String sortBy, String sortOrder) {
        Sort.Direction direction = "desc".equalsIgnoreCase(sortOrder) ? Sort.Direction.DESC : Sort.Direction.ASC;

        switch (sortBy != null ? sortBy.toLowerCase() : "name") {
            case "price":
                // 按价格排序需要特殊处理，这里先按名称排序
                return Sort.by(direction, "name");
            case "rating":
                return Sort.by(direction, "starRating");
            case "city":
                return Sort.by(direction, "city");
            case "name":
            default:
                return Sort.by(direction, "name");
        }
    }

    // ==================== 仪表盘统计方法 ====================

    /**
     * 获取平均评分
     */
    @Transactional(readOnly = true)
    public double getAverageRating() {
        try {
            List<HotelDto> hotels = getAllActiveHotels();
            if (hotels.isEmpty()) {
                return 0.0;
            }

            double totalRating = hotels.stream()
                    .filter(hotel -> hotel.getAverageRating() != null)
                    .mapToDouble(HotelDto::getAverageRating)
                    .sum();

            long hotelCount = hotels.stream()
                    .filter(hotel -> hotel.getAverageRating() != null)
                    .count();

            return hotelCount > 0 ? totalRating / hotelCount : 0.0;
        } catch (Exception e) {
            logger.warn("获取酒店平均评分失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 获取今日新增酒店数量
     */
    @Transactional(readOnly = true)
    public int getTodayNewHotelCount() {
        try {
            LocalDate today = LocalDate.now();
            LocalDateTime startOfDay = today.atStartOfDay();
            LocalDateTime endOfDay = today.atTime(23, 59, 59);

            List<Hotel> allHotels = hotelRepository.findAll();
            return (int) allHotels.stream()
                    .filter(hotel -> hotel.getCreatedAt() != null)
                    .filter(hotel -> hotel.getCreatedAt().isAfter(startOfDay) &&
                            hotel.getCreatedAt().isBefore(endOfDay))
                    .count();
        } catch (Exception e) {
            logger.warn("获取今日新增酒店数量失败: {}", e.getMessage());
            return 0;
        }
    }
}
