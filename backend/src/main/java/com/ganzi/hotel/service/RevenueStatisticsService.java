package com.ganzi.hotel.service;

import com.ganzi.hotel.repository.BookingRepository;
import com.ganzi.hotel.repository.PackageBookingRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 收入统计服务类
 * 提供酒店和套餐收入占比统计功能
 */
@Service
public class RevenueStatisticsService {

    private static final Logger logger = LoggerFactory.getLogger(RevenueStatisticsService.class);

    @Autowired
    private BookingRepository bookingRepository;

    @Autowired
    private PackageBookingRepository packageBookingRepository;

    /**
     * 获取收入占比统计数据
     * 只统计支付状态为 PAID 的订单收入
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getRevenueBreakdown() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 获取酒店收入数据
            Map<String, Object> hotelRevenue = getHotelRevenueBreakdown();
            
            // 获取套餐收入数据
            Map<String, Object> packageRevenue = getPackageRevenueBreakdown();

            // 计算总收入
            BigDecimal hotelTotal = (BigDecimal) hotelRevenue.get("total");
            BigDecimal packageTotal = (BigDecimal) packageRevenue.get("total");
            BigDecimal totalRevenue = hotelTotal.add(packageTotal);

            // 计算酒店和套餐收入的总体占比
            if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal hotelPercentage = hotelTotal.multiply(BigDecimal.valueOf(100))
                    .divide(totalRevenue, 2, RoundingMode.HALF_UP);
                BigDecimal packagePercentage = packageTotal.multiply(BigDecimal.valueOf(100))
                    .divide(totalRevenue, 2, RoundingMode.HALF_UP);
                
                hotelRevenue.put("percentage", hotelPercentage);
                packageRevenue.put("percentage", packagePercentage);
            } else {
                hotelRevenue.put("percentage", BigDecimal.ZERO);
                packageRevenue.put("percentage", BigDecimal.ZERO);
            }

            result.put("hotelRevenue", hotelRevenue);
            result.put("packageRevenue", packageRevenue);
            result.put("totalRevenue", totalRevenue);

            logger.info("收入占比统计完成 - 总收入: {}, 酒店收入: {}, 套餐收入: {}", 
                       totalRevenue, hotelTotal, packageTotal);

            return result;
        } catch (Exception e) {
            logger.error("获取收入占比统计失败", e);
            throw new RuntimeException("获取收入占比统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取酒店收入占比统计
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getHotelRevenueBreakdown() {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> breakdown = new ArrayList<>();

            // 获取酒店收入数据
            List<Object[]> hotelRevenueData = bookingRepository.findRevenueByHotel();
            BigDecimal totalHotelRevenue = bookingRepository.calculateTotalHotelRevenue();

            // 处理每个酒店的收入数据
            for (Object[] row : hotelRevenueData) {
                Long hotelId = (Long) row[0];
                String hotelName = (String) row[1];
                BigDecimal revenue = (BigDecimal) row[2];

                Map<String, Object> hotelData = new HashMap<>();
                hotelData.put("hotelId", hotelId);
                hotelData.put("hotelName", hotelName);
                hotelData.put("revenue", revenue);

                // 计算该酒店收入占酒店总收入的百分比
                if (totalHotelRevenue.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal percentage = revenue.multiply(BigDecimal.valueOf(100))
                        .divide(totalHotelRevenue, 2, RoundingMode.HALF_UP);
                    hotelData.put("percentage", percentage);
                } else {
                    hotelData.put("percentage", BigDecimal.ZERO);
                }

                breakdown.add(hotelData);
            }

            result.put("total", totalHotelRevenue);
            result.put("breakdown", breakdown);

            return result;
        } catch (Exception e) {
            logger.error("获取酒店收入统计失败", e);
            throw new RuntimeException("获取酒店收入统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取套餐收入占比统计
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getPackageRevenueBreakdown() {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> breakdown = new ArrayList<>();

            // 获取套餐收入数据
            List<Object[]> packageRevenueData = packageBookingRepository.findRevenueByPackage();
            BigDecimal totalPackageRevenue = packageBookingRepository.calculateTotalPackageRevenue();

            // 处理每个套餐的收入数据
            for (Object[] row : packageRevenueData) {
                Long packageId = (Long) row[0];
                String packageName = (String) row[1];
                BigDecimal revenue = (BigDecimal) row[2];

                Map<String, Object> packageData = new HashMap<>();
                packageData.put("packageId", packageId);
                packageData.put("packageName", packageName);
                packageData.put("revenue", revenue);

                // 计算该套餐收入占套餐总收入的百分比
                if (totalPackageRevenue.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal percentage = revenue.multiply(BigDecimal.valueOf(100))
                        .divide(totalPackageRevenue, 2, RoundingMode.HALF_UP);
                    packageData.put("percentage", percentage);
                } else {
                    packageData.put("percentage", BigDecimal.ZERO);
                }

                breakdown.add(packageData);
            }

            result.put("total", totalPackageRevenue);
            result.put("breakdown", breakdown);

            return result;
        } catch (Exception e) {
            logger.error("获取套餐收入统计失败", e);
            throw new RuntimeException("获取套餐收入统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取收入统计摘要
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getRevenueSummary() {
        try {
            Map<String, Object> summary = new HashMap<>();

            BigDecimal hotelRevenue = bookingRepository.calculateTotalHotelRevenue();
            BigDecimal packageRevenue = packageBookingRepository.calculateTotalPackageRevenue();
            BigDecimal totalRevenue = hotelRevenue.add(packageRevenue);

            summary.put("hotelRevenue", hotelRevenue);
            summary.put("packageRevenue", packageRevenue);
            summary.put("totalRevenue", totalRevenue);

            // 计算占比
            if (totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal hotelPercentage = hotelRevenue.multiply(BigDecimal.valueOf(100))
                    .divide(totalRevenue, 2, RoundingMode.HALF_UP);
                BigDecimal packagePercentage = packageRevenue.multiply(BigDecimal.valueOf(100))
                    .divide(totalRevenue, 2, RoundingMode.HALF_UP);
                
                summary.put("hotelPercentage", hotelPercentage);
                summary.put("packagePercentage", packagePercentage);
            } else {
                summary.put("hotelPercentage", BigDecimal.ZERO);
                summary.put("packagePercentage", BigDecimal.ZERO);
            }

            return summary;
        } catch (Exception e) {
            logger.error("获取收入摘要失败", e);
            throw new RuntimeException("获取收入摘要失败: " + e.getMessage());
        }
    }
}
