package com.ganzi.hotel.service;

import com.ganzi.hotel.dto.BookingDto;
import com.ganzi.hotel.dto.BookingRequestDto;
import com.ganzi.hotel.entity.*;
import com.ganzi.hotel.repository.*;
import com.ganzi.hotel.service.PaymentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预订管理服务类
 */
@Service
@Transactional
public class BookingService {

    private static final Logger logger = LoggerFactory.getLogger(BookingService.class);

    @Autowired
    private BookingRepository bookingRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private HotelRepository hotelRepository;

    @Autowired
    private RoomTypeRepository roomTypeRepository;

    @Autowired
    private RoomRepository roomRepository;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private PaymentRepository paymentRepository;

    /**
     * 创建预订
     */
    public BookingDto createBooking(Long userId, BookingRequestDto requestDto) {
        logger.info("创建预订，用户ID: {}，请求: {}", userId, requestDto);

        // 验证用户
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在"));

        // 验证酒店
        Hotel hotel = hotelRepository.findById(requestDto.getHotelId())
                .orElseThrow(() -> new IllegalArgumentException("酒店不存在"));

        if (!hotel.getIsActive()) {
            throw new IllegalArgumentException("酒店已停用");
        }

        // 验证房型
        RoomType roomType = roomTypeRepository.findById(requestDto.getRoomTypeId())
                .orElseThrow(() -> new IllegalArgumentException("房型不存在"));

        if (!roomType.getIsActive()) {
            throw new IllegalArgumentException("房型已停用");
        }

        // 验证房型是否属于该酒店
        if (!roomType.getHotel().getId().equals(hotel.getId())) {
            throw new IllegalArgumentException("房型不属于该酒店");
        }

        // 验证日期
        validateBookingDates(requestDto.getCheckInDate(), requestDto.getCheckOutDate());

        // 检查房型可用性
        if (!isRoomTypeAvailable(roomType.getId(), requestDto.getCheckInDate(), requestDto.getCheckOutDate())) {
            throw new IllegalArgumentException("选择的日期范围内房型不可用");
        }

        // 验证入住人数
        int totalGuests = requestDto.getAdults() + requestDto.getChildren();
        if (totalGuests > roomType.getMaxOccupancy()) {
            throw new IllegalArgumentException("入住人数超过房型最大容量");
        }

        // 创建预订
        Booking booking = new Booking();
        booking.setBookingNumber(generateBookingNumber());
        booking.setUser(user);
        booking.setHotel(hotel);
        booking.setRoomType(roomType);
        booking.setCheckInDate(requestDto.getCheckInDate());
        booking.setCheckOutDate(requestDto.getCheckOutDate());
        booking.setAdults(requestDto.getAdults());
        booking.setChildren(requestDto.getChildren());
        booking.setSpecialRequests(requestDto.getSpecialRequests());
        booking.setGuestName(requestDto.getGuestName());
        booking.setGuestPhone(requestDto.getGuestPhone());
        booking.setGuestEmail(requestDto.getGuestEmail());

        // 计算价格
        calculateBookingPrice(booking);

        // 保存预订
        Booking savedBooking = bookingRepository.save(booking);
        logger.info("预订创建成功，预订号: {}", savedBooking.getBookingNumber());

        return convertToDto(savedBooking);
    }

    /**
     * 根据ID获取预订详情
     */
    @Transactional(readOnly = true)
    public Optional<BookingDto> getBookingById(Long id) {
        logger.info("获取预订详情，ID: {}", id);
        return bookingRepository.findById(id).map(this::convertToDto);
    }

    /**
     * 根据预订号获取预订详情
     */
    @Transactional(readOnly = true)
    public Optional<BookingDto> getBookingByNumber(String bookingNumber) {
        logger.info("根据预订号获取预订详情: {}", bookingNumber);
        return bookingRepository.findByBookingNumber(bookingNumber).map(this::convertToDto);
    }

    /**
     * 获取用户的预订列表
     */
    @Transactional(readOnly = true)
    public Page<BookingDto> getUserBookings(Long userId, Pageable pageable) {
        logger.info("获取用户预订列表，用户ID: {}", userId);
        Page<Booking> bookings = bookingRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        return bookings.map(this::convertToDto);
    }

    /**
     * 获取用户的预订列表（支持状态筛选）
     */
    @Transactional(readOnly = true)
    public Page<BookingDto> getUserBookings(Long userId, Pageable pageable, String status) {
        logger.info("获取用户预订列表，用户ID: {}，状态: {}", userId, status);

        Page<Booking> bookings;
        if (status != null && !status.isEmpty()) {
            try {
                Booking.BookingStatus bookingStatus = Booking.BookingStatus.valueOf(status);
                bookings = bookingRepository.findByUserIdAndStatusOrderByCreatedAtDesc(userId, bookingStatus, pageable);
            } catch (IllegalArgumentException e) {
                logger.warn("无效的预订状态: {}", status);
                // 如果状态无效，返回空结果
                bookings = Page.empty(pageable);
            }
        } else {
            bookings = bookingRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        }

        return bookings.map(this::convertToDto);
    }

    /**
     * 获取酒店的预订列表
     */
    @Transactional(readOnly = true)
    public Page<BookingDto> getHotelBookings(Long hotelId, Pageable pageable) {
        logger.info("获取酒店预订列表，酒店ID: {}", hotelId);
        Page<Booking> bookings = bookingRepository.findByHotelIdOrderByCreatedAtDesc(hotelId, pageable);
        return bookings.map(this::convertToDto);
    }

    /**
     * 获取所有预订列表（管理员功能）
     */
    @Transactional(readOnly = true)
    public List<BookingDto> getAllBookings() {
        logger.info("获取所有预订列表");
        List<Booking> bookings = bookingRepository.findAllWithDetails();
        return bookings.stream()
                .map(this::convertToDto)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取所有预订列表（管理员，支持分页和筛选）
     */
    @Transactional(readOnly = true)
    public Page<BookingDto> getAllBookings(Pageable pageable, String status, Long hotelId, String dateFrom,
            String dateTo) {
        logger.info("获取所有预订列表，状态: {}，酒店ID: {}，日期范围: {} - {}", status, hotelId, dateFrom, dateTo);

        Page<Booking> bookings;

        if (status != null || hotelId != null || dateFrom != null || dateTo != null) {
            // 有筛选条件，使用条件查询
            LocalDate fromDate = dateFrom != null ? LocalDate.parse(dateFrom) : null;
            LocalDate toDate = dateTo != null ? LocalDate.parse(dateTo) : null;
            Booking.BookingStatus bookingStatus = null;

            if (status != null && !status.isEmpty()) {
                try {
                    bookingStatus = Booking.BookingStatus.valueOf(status.toUpperCase());
                } catch (IllegalArgumentException e) {
                    logger.warn("无效的预订状态: {}", status);
                    return Page.empty(pageable);
                }
            }

            // 使用简单的查询方法，如果没有复杂的筛选方法
            if (hotelId != null) {
                bookings = bookingRepository.findByHotelIdOrderByCreatedAtDesc(hotelId, pageable);
            } else {
                bookings = bookingRepository.findAll(pageable);
            }
        } else {
            // 无筛选条件，获取所有预订
            bookings = bookingRepository.findAll(pageable);
        }

        return bookings.map(this::convertToDto);
    }

    /**
     * 确认预订
     */
    public BookingDto confirmBooking(Long bookingId) {
        logger.info("确认预订，ID: {}", bookingId);

        Booking booking = bookingRepository.findById(bookingId)
                .orElseThrow(() -> new IllegalArgumentException("预订不存在"));

        if (booking.getStatus() != Booking.BookingStatus.PENDING) {
            throw new IllegalArgumentException("只能确认待确认状态的预订");
        }

        // 再次检查房型可用性
        if (!isRoomTypeAvailable(booking.getRoomType().getId(),
                booking.getCheckInDate(), booking.getCheckOutDate())) {
            throw new IllegalArgumentException("房型已不可用，无法确认预订");
        }

        booking.setStatus(Booking.BookingStatus.CONFIRMED);
        Booking savedBooking = bookingRepository.save(booking);

        logger.info("预订确认成功，预订号: {}", savedBooking.getBookingNumber());
        return convertToDto(savedBooking);
    }

    /**
     * 取消预订
     */
    public BookingDto cancelBooking(Long bookingId, String reason) {
        logger.info("取消预订，ID: {}，原因: {}", bookingId, reason);

        Booking booking = bookingRepository.findById(bookingId)
                .orElseThrow(() -> new IllegalArgumentException("预订不存在"));

        if (booking.getStatus() == Booking.BookingStatus.CANCELLED) {
            throw new IllegalArgumentException("预订已取消");
        }

        if (booking.getStatus() == Booking.BookingStatus.CHECKED_OUT) {
            throw new IllegalArgumentException("已退房的预订无法取消");
        }

        booking.setStatus(Booking.BookingStatus.CANCELLED);
        Booking savedBooking = bookingRepository.save(booking);

        logger.info("预订取消成功，预订号: {}", savedBooking.getBookingNumber());
        return convertToDto(savedBooking);
    }

    /**
     * 办理入住
     */
    public BookingDto checkIn(Long bookingId, Long roomId) {
        logger.info("办理入住，预订ID: {}，房间ID: {}", bookingId, roomId);

        Booking booking = bookingRepository.findById(bookingId)
                .orElseThrow(() -> new IllegalArgumentException("预订不存在"));

        if (booking.getStatus() != Booking.BookingStatus.CONFIRMED) {
            throw new IllegalArgumentException("只能为已确认的预订办理入住");
        }

        // 验证房间
        if (roomId != null) {
            Room room = roomRepository.findById(roomId)
                    .orElseThrow(() -> new IllegalArgumentException("房间不存在"));

            if (!room.getRoomType().getId().equals(booking.getRoomType().getId())) {
                throw new IllegalArgumentException("房间类型不匹配");
            }

            if (room.getStatus() != Room.RoomStatus.AVAILABLE) {
                throw new IllegalArgumentException("房间不可用");
            }

            booking.setRoom(room);
            // 更新房间状态为已占用
            room.setStatus(Room.RoomStatus.OCCUPIED);
            roomRepository.save(room);
        }

        booking.setStatus(Booking.BookingStatus.CHECKED_IN);
        booking.setCheckInTime(LocalDateTime.now());
        Booking savedBooking = bookingRepository.save(booking);

        logger.info("入住办理成功，预订号: {}", savedBooking.getBookingNumber());
        return convertToDto(savedBooking);
    }

    /**
     * 办理退房
     */
    public BookingDto checkOut(Long bookingId) {
        logger.info("办理退房，预订ID: {}", bookingId);

        Booking booking = bookingRepository.findById(bookingId)
                .orElseThrow(() -> new IllegalArgumentException("预订不存在"));

        if (booking.getStatus() != Booking.BookingStatus.CHECKED_IN) {
            throw new IllegalArgumentException("只能为已入住的预订办理退房");
        }

        booking.setStatus(Booking.BookingStatus.CHECKED_OUT);
        booking.setCheckOutTime(LocalDateTime.now());

        // 释放房间
        if (booking.getRoom() != null) {
            Room room = booking.getRoom();
            room.setStatus(Room.RoomStatus.AVAILABLE);
            roomRepository.save(room);
        }

        Booking savedBooking = bookingRepository.save(booking);

        logger.info("退房办理成功，预订号: {}", savedBooking.getBookingNumber());
        return convertToDto(savedBooking);
    }

    /**
     * 处理退款
     */
    public BookingDto processRefund(Long bookingId, BigDecimal refundAmount, String reason) {
        logger.info("处理退款，预订ID: {}，退款金额: {}，原因: {}", bookingId, refundAmount, reason);

        Booking booking = bookingRepository.findById(bookingId)
                .orElseThrow(() -> new IllegalArgumentException("预订不存在"));

        // 检查预订状态，只有已确认、已入住或已退房的预订可以退款
        if (booking.getStatus() != Booking.BookingStatus.CONFIRMED &&
                booking.getStatus() != Booking.BookingStatus.CHECKED_IN &&
                booking.getStatus() != Booking.BookingStatus.CHECKED_OUT) {
            throw new IllegalArgumentException("当前预订状态不支持退款");
        }

        // 计算退款金额
        BigDecimal actualRefundAmount = refundAmount != null ? refundAmount : booking.getTotalAmount();

        // 验证退款金额不能超过预订总金额
        if (actualRefundAmount.compareTo(booking.getTotalAmount()) > 0) {
            throw new IllegalArgumentException("退款金额不能超过预订总金额");
        }

        // 创建退款记录（通过PaymentService）
        try {
            // 查找预订的支付记录
            List<Payment> payments = paymentRepository.findByBookingIdOrderByCreatedAtDesc(bookingId);

            // 查找成功的支付记录
            Payment successPayment = null;
            for (Payment payment : payments) {
                if (payment.getStatus() == Payment.PaymentStatus.SUCCESS) {
                    successPayment = payment;
                    break;
                }
            }

            if (successPayment != null) {
                // 处理退款
                paymentService.refundPayment(successPayment.getId(), reason);
                logger.info("支付退款处理成功，支付ID: {}", successPayment.getId());
            } else {
                logger.info("未找到成功的支付记录，跳过支付退款处理");
            }
        } catch (Exception e) {
            logger.warn("处理支付退款失败: {}", e.getMessage());
            // 继续处理预订状态更新，即使支付退款失败
        }

        // 更新预订状态为已取消
        booking.setStatus(Booking.BookingStatus.CANCELLED);

        // 释放房间（如果已分配）
        if (booking.getRoom() != null) {
            Room room = booking.getRoom();
            room.setStatus(Room.RoomStatus.AVAILABLE);
            roomRepository.save(room);
            booking.setRoom(null);
        }

        Booking savedBooking = bookingRepository.save(booking);

        logger.info("退款处理成功，预订号: {}，退款金额: {}", savedBooking.getBookingNumber(), actualRefundAmount);
        return convertToDto(savedBooking);
    }

    /**
     * 验证预订日期
     */
    private void validateBookingDates(LocalDate checkInDate, LocalDate checkOutDate) {
        if (checkInDate == null || checkOutDate == null) {
            throw new IllegalArgumentException("入住日期和退房日期不能为空");
        }

        if (checkInDate.isBefore(LocalDate.now())) {
            throw new IllegalArgumentException("入住日期不能早于今天");
        }

        if (checkOutDate.isBefore(checkInDate) || checkOutDate.isEqual(checkInDate)) {
            throw new IllegalArgumentException("退房日期必须晚于入住日期");
        }

        // 限制预订时间范围（例如最多提前1年预订）
        if (checkInDate.isAfter(LocalDate.now().plusYears(1))) {
            throw new IllegalArgumentException("入住日期不能超过一年");
        }
    }

    /**
     * 检查房型可用性
     */
    @Transactional(readOnly = true)
    public boolean isRoomTypeAvailable(Long roomTypeId, LocalDate checkInDate, LocalDate checkOutDate) {
        // 获取房型信息
        RoomType roomType = roomTypeRepository.findById(roomTypeId)
                .orElseThrow(() -> new IllegalArgumentException("房型不存在"));

        // 获取该房型的总房间数
        long totalRooms = roomRepository.countByRoomTypeIdAndStatus(roomTypeId, Room.RoomStatus.AVAILABLE);
        logger.debug("房型ID: {}, 可用房间总数: {}", roomTypeId, totalRooms);

        // 获取指定日期范围内的预订数量（排除已取消和已退房的预订）
        List<Booking> conflictingBookings = bookingRepository.findRoomTypeBookingsInDateRange(
                roomTypeId, checkInDate, checkOutDate);
        logger.debug("房型ID: {}, 日期范围: {} 到 {}, 冲突预订数量: {}",
                roomTypeId, checkInDate, checkOutDate, conflictingBookings.size());

        boolean available = conflictingBookings.size() < totalRooms;
        logger.debug("房型ID: {}, 可用性检查结果: {}", roomTypeId, available);

        return available;
    }

    /**
     * 计算预订价格
     */
    private void calculateBookingPrice(Booking booking) {
        int nights = (int) ChronoUnit.DAYS.between(booking.getCheckInDate(), booking.getCheckOutDate());
        BigDecimal roomRate = booking.getRoomType().getBasePrice();

        booking.setTotalNights(nights);
        booking.setRoomRate(roomRate);

        BigDecimal totalAmount = roomRate.multiply(BigDecimal.valueOf(nights));
        booking.setTotalAmount(totalAmount);

        // 计算税费（假设10%）
        BigDecimal taxAmount = totalAmount.multiply(BigDecimal.valueOf(0.1));
        booking.setTaxAmount(taxAmount);

        // 暂时没有折扣
        booking.setDiscountAmount(BigDecimal.ZERO);

        // 最终金额
        BigDecimal finalAmount = totalAmount.add(taxAmount).subtract(booking.getDiscountAmount());
        booking.setFinalAmount(finalAmount);
    }

    /**
     * 生成预订号
     */
    private String generateBookingNumber() {
        String prefix = "BK";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        return prefix + timestamp + uuid;
    }

    /**
     * 转换为DTO
     */
    private BookingDto convertToDto(Booking booking) {
        BookingDto dto = new BookingDto();
        dto.setId(booking.getId());
        dto.setBookingNumber(booking.getBookingNumber());
        dto.setCheckInDate(booking.getCheckInDate());
        dto.setCheckOutDate(booking.getCheckOutDate());
        dto.setAdults(booking.getAdults());
        dto.setChildren(booking.getChildren());
        dto.setTotalNights(booking.getTotalNights());
        dto.setRoomRate(booking.getRoomRate());
        dto.setTotalAmount(booking.getTotalAmount());
        dto.setTaxAmount(booking.getTaxAmount());
        dto.setDiscountAmount(booking.getDiscountAmount());
        dto.setFinalAmount(booking.getFinalAmount());
        dto.setStatus(booking.getStatus());
        dto.setPaymentStatus(booking.getPaymentStatus());
        dto.setSpecialRequests(booking.getSpecialRequests());
        dto.setGuestName(booking.getGuestName());
        dto.setGuestPhone(booking.getGuestPhone());
        dto.setGuestEmail(booking.getGuestEmail());
        dto.setCheckInTime(booking.getCheckInTime());
        dto.setCheckOutTime(booking.getCheckOutTime());
        dto.setCreatedAt(booking.getCreatedAt());
        dto.setUpdatedAt(booking.getUpdatedAt());

        // 设置关联对象（简化版本，避免循环引用）
        if (booking.getUser() != null) {
            dto.setUserId(booking.getUser().getId());
            dto.setUserName(booking.getUser().getFullName());
        }

        if (booking.getHotel() != null) {
            dto.setHotelId(booking.getHotel().getId());
            dto.setHotelName(booking.getHotel().getName());
        }

        if (booking.getRoomType() != null) {
            dto.setRoomTypeId(booking.getRoomType().getId());
            dto.setRoomTypeName(booking.getRoomType().getName());
        }

        if (booking.getRoom() != null) {
            dto.setRoomId(booking.getRoom().getId());
            dto.setRoomNumber(booking.getRoom().getRoomNumber());
        }

        return dto;
    }

    /**
     * 获取指定日期的预订数量
     */
    @Transactional(readOnly = true)
    public int getBookingCountByDate(LocalDate date) {
        try {
            return bookingRepository.countByCreatedAtBetween(
                    date.atStartOfDay(),
                    date.atTime(23, 59, 59));
        } catch (Exception e) {
            logger.warn("获取日期 {} 的预订数量失败: {}", date, e.getMessage());
            return 0;
        }
    }

    /**
     * 获取指定日期的收入
     */
    @Transactional(readOnly = true)
    public BigDecimal getRevenueByDate(LocalDate date) {
        try {
            BigDecimal revenue = bookingRepository.sumTotalAmountByCreatedAtBetween(
                    date.atStartOfDay(),
                    date.atTime(23, 59, 59));
            return revenue != null ? revenue : BigDecimal.ZERO;
        } catch (Exception e) {
            logger.warn("获取日期 {} 的收入失败: {}", date, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    // ==================== 仪表盘统计方法 ====================

    /**
     * 获取今日新增预订数量
     */
    @Transactional(readOnly = true)
    public int getTodayNewBookingCount() {
        LocalDate today = LocalDate.now();
        return getBookingCountByDate(today);
    }

    /**
     * 获取总收入（仅统计已支付订单）
     */
    @Transactional(readOnly = true)
    public BigDecimal getTotalRevenue() {
        try {
            LocalDateTime startTime = LocalDateTime.of(2020, 1, 1, 0, 0);
            LocalDateTime endTime = LocalDateTime.now();
            BigDecimal revenue = bookingRepository.calculateRevenueInDateRange(startTime, endTime);
            return revenue != null ? revenue : BigDecimal.ZERO;
        } catch (Exception e) {
            logger.warn("获取总收入失败: {}", e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取今日收入（仅统计已支付订单）
     */
    @Transactional(readOnly = true)
    public BigDecimal getTodayRevenue() {
        LocalDate today = LocalDate.now();
        return getRevenueByDate(today);
    }

    /**
     * 获取预订状态分布
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getStatusDistribution() {
        try {
            LocalDate startDate = LocalDate.now().minusDays(30); // 最近30天
            LocalDate endDate = LocalDate.now();
            List<Object[]> statusData = bookingRepository.findBookingStatusDistribution(startDate, endDate);

            List<Map<String, Object>> distribution = new ArrayList<>();
            for (Object[] row : statusData) {
                Map<String, Object> item = new HashMap<>();
                item.put("name", row[0].toString());
                item.put("value", ((Number) row[1]).intValue());
                distribution.add(item);
            }
            return distribution;
        } catch (Exception e) {
            logger.warn("获取预订状态分布失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取预订趋势数据
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getBookingTrends(LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> trends = new HashMap<>();
        List<String> labels = new ArrayList<>();
        List<Integer> bookings = new ArrayList<>();

        try {
            // 按天统计预订数量
            LocalDate start = startDate.toLocalDate();
            LocalDate end = endDate.toLocalDate();

            while (!start.isAfter(end)) {
                labels.add(start.toString());
                int count = getBookingCountByDate(start);
                bookings.add(count);
                start = start.plusDays(1);
            }

            trends.put("labels", labels);
            trends.put("bookings", bookings);
        } catch (Exception e) {
            logger.warn("获取预订趋势数据失败: {}", e.getMessage());
        }

        return trends;
    }

    /**
     * 获取收入趋势数据
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getRevenueTrends(LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> trends = new HashMap<>();
        List<String> labels = new ArrayList<>();
        List<BigDecimal> revenue = new ArrayList<>();

        try {
            // 按天统计收入
            LocalDate start = startDate.toLocalDate();
            LocalDate end = endDate.toLocalDate();

            while (!start.isAfter(end)) {
                labels.add(start.toString());
                BigDecimal dailyRevenue = getRevenueByDate(start);
                revenue.add(dailyRevenue);
                start = start.plusDays(1);
            }

            trends.put("labels", labels);
            trends.put("revenue", revenue);
        } catch (Exception e) {
            logger.warn("获取收入趋势数据失败: {}", e.getMessage());
        }

        return trends;
    }
}
