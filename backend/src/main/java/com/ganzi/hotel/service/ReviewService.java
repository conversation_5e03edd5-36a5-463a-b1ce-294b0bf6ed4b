package com.ganzi.hotel.service;

import com.ganzi.hotel.dto.ReviewDto;
import com.ganzi.hotel.dto.ReviewRequestDto;
import com.ganzi.hotel.entity.Booking;
import com.ganzi.hotel.entity.Hotel;
import com.ganzi.hotel.entity.Review;
import com.ganzi.hotel.entity.User;
import com.ganzi.hotel.repository.BookingRepository;
import com.ganzi.hotel.repository.HotelRepository;
import com.ganzi.hotel.repository.ReviewRepository;
import com.ganzi.hotel.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 评价管理服务类
 */
@Service
@Transactional
public class ReviewService {

    private static final Logger logger = LoggerFactory.getLogger(ReviewService.class);

    @Autowired
    private ReviewRepository reviewRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private HotelRepository hotelRepository;

    @Autowired
    private BookingRepository bookingRepository;

    /**
     * 创建评价
     */
    public ReviewDto createReview(Long userId, ReviewRequestDto requestDto) {
        logger.info("创建评价，用户ID: {}，请求: {}", userId, requestDto);

        // 验证用户
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("用户不存在"));

        // 验证酒店
        Hotel hotel = hotelRepository.findById(requestDto.getHotelId())
                .orElseThrow(() -> new IllegalArgumentException("酒店不存在"));

        // 验证预订（如果提供了预订ID）
        Booking booking = null;
        if (requestDto.getBookingId() != null) {
            booking = bookingRepository.findById(requestDto.getBookingId())
                    .orElseThrow(() -> new IllegalArgumentException("预订不存在"));

            // 验证预订是否属于该用户
            if (!booking.getUser().getId().equals(userId)) {
                throw new IllegalArgumentException("预订不属于当前用户");
            }

            // 验证预订是否已完成
            if (booking.getStatus() != Booking.BookingStatus.CHECKED_OUT) {
                throw new IllegalArgumentException("只能对已完成的预订进行评价");
            }

            // 检查是否已经评价过该预订
            if (reviewRepository.existsByUserIdAndBookingId(userId, requestDto.getBookingId())) {
                throw new IllegalArgumentException("该预订已经评价过了");
            }
        } else {
            // 如果没有预订ID，检查是否已经评价过该酒店
            if (reviewRepository.existsByUserIdAndHotelId(userId, requestDto.getHotelId())) {
                throw new IllegalArgumentException("您已经评价过该酒店了");
            }
        }

        // 创建评价
        Review review = new Review();
        review.setUser(user);
        review.setHotel(hotel);
        review.setBooking(booking);
        review.setRating(requestDto.getRating());
        review.setServiceRating(requestDto.getServiceRating());
        review.setCleanlinessRating(requestDto.getCleanlinessRating());
        review.setLocationRating(requestDto.getLocationRating());
        review.setValueRating(requestDto.getValueRating());
        review.setComment(requestDto.getComment());
        review.setStatus(Review.ReviewStatus.PENDING); // 默认待审核

        // 保存评价
        Review savedReview = reviewRepository.save(review);
        logger.info("评价创建成功，ID: {}", savedReview.getId());

        return convertToDto(savedReview);
    }

    /**
     * 审核评价
     */
    public ReviewDto approveReview(Long reviewId, boolean approved, String reason) {
        logger.info("审核评价，ID: {}，通过: {}，原因: {}", reviewId, approved, reason);

        Review review = reviewRepository.findById(reviewId)
                .orElseThrow(() -> new IllegalArgumentException("评价不存在"));

        if (review.getStatus() != Review.ReviewStatus.PENDING) {
            throw new IllegalArgumentException("只能审核待审核状态的评价");
        }

        review.setStatus(approved ? Review.ReviewStatus.APPROVED : Review.ReviewStatus.REJECTED);
        if (!approved && reason != null) {
            review.setAdminReply(reason);
            review.setAdminReplyAt(LocalDateTime.now());
        }

        Review savedReview = reviewRepository.save(review);
        logger.info("评价审核完成，ID: {}，状态: {}", savedReview.getId(), savedReview.getStatus());

        return convertToDto(savedReview);
    }

    /**
     * 管理员回复评价
     */
    public ReviewDto replyToReview(Long reviewId, String reply) {
        logger.info("管理员回复评价，ID: {}，回复: {}", reviewId, reply);

        Review review = reviewRepository.findById(reviewId)
                .orElseThrow(() -> new IllegalArgumentException("评价不存在"));

        if (review.getStatus() != Review.ReviewStatus.APPROVED) {
            throw new IllegalArgumentException("只能回复已通过的评价");
        }

        review.setAdminReply(reply);
        review.setAdminReplyAt(LocalDateTime.now());

        Review savedReview = reviewRepository.save(review);
        logger.info("管理员回复成功，评价ID: {}", savedReview.getId());

        return convertToDto(savedReview);
    }

    /**
     * 隐藏评价
     */
    public ReviewDto hideReview(Long reviewId, String reason) {
        logger.info("隐藏评价，ID: {}，原因: {}", reviewId, reason);

        Review review = reviewRepository.findById(reviewId)
                .orElseThrow(() -> new IllegalArgumentException("评价不存在"));

        review.setStatus(Review.ReviewStatus.HIDDEN);
        if (reason != null) {
            review.setAdminReply(reason);
            review.setAdminReplyAt(LocalDateTime.now());
        }

        Review savedReview = reviewRepository.save(review);
        logger.info("评价隐藏成功，ID: {}", savedReview.getId());

        return convertToDto(savedReview);
    }

    /**
     * 根据ID获取评价详情
     */
    @Transactional(readOnly = true)
    public Optional<ReviewDto> getReviewById(Long id) {
        logger.info("获取评价详情，ID: {}", id);
        return reviewRepository.findById(id).map(this::convertToDto);
    }

    /**
     * 获取酒店的评价列表
     */
    @Transactional(readOnly = true)
    public Page<ReviewDto> getHotelReviews(Long hotelId, Pageable pageable) {
        logger.info("获取酒店评价列表，酒店ID: {}", hotelId);
        Page<Review> reviews = reviewRepository.findByHotelIdAndStatusOrderByCreatedAtDesc(
                hotelId, Review.ReviewStatus.APPROVED, pageable);
        return reviews.map(this::convertToDto);
    }

    /**
     * 获取用户的评价列表
     */
    @Transactional(readOnly = true)
    public Page<ReviewDto> getUserReviews(Long userId, Pageable pageable) {
        logger.info("获取用户评价列表，用户ID: {}", userId);
        Page<Review> reviews = reviewRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        return reviews.map(this::convertToDto);
    }

    /**
     * 获取待审核的评价列表
     */
    @Transactional(readOnly = true)
    public Page<ReviewDto> getPendingReviews(Pageable pageable) {
        logger.info("获取待审核评价列表");
        Page<Review> reviews = reviewRepository.findByStatusOrderByCreatedAtDesc(
                Review.ReviewStatus.PENDING, pageable);
        return reviews.map(this::convertToDto);
    }

    /**
     * 获取酒店评价统计信息
     */
    @Transactional(readOnly = true)
    public ReviewStatisticsDto getHotelReviewStatistics(Long hotelId) {
        logger.info("获取酒店评价统计，酒店ID: {}", hotelId);

        ReviewStatisticsDto statistics = new ReviewStatisticsDto();
        statistics.setHotelId(hotelId);

        // 总评价数
        long totalReviews = reviewRepository.countApprovedReviewsByHotel(hotelId);
        statistics.setTotalReviews(totalReviews);

        if (totalReviews > 0) {
            // 平均评分
            Double averageRating = reviewRepository.calculateAverageRatingByHotel(hotelId);
            statistics.setAverageRating(averageRating != null ? averageRating : 0.0);

            // 详细评分
            Object[] detailedRatings = reviewRepository.calculateDetailedRatingsByHotel(hotelId);
            if (detailedRatings != null && detailedRatings.length >= 5) {
                statistics.setOverallRating((Double) detailedRatings[0]);
                statistics.setServiceRating((Double) detailedRatings[1]);
                statistics.setCleanlinessRating((Double) detailedRatings[2]);
                statistics.setLocationRating((Double) detailedRatings[3]);
                statistics.setValueRating((Double) detailedRatings[4]);
            }

            // 评分分布
            List<Object[]> ratingDistribution = reviewRepository.countReviewsByRating(hotelId);
            statistics.setRatingDistribution(ratingDistribution);
        }

        return statistics;
    }

    /**
     * 检查用户是否可以评价酒店
     */
    @Transactional(readOnly = true)
    public boolean canUserReviewHotel(Long userId, Long hotelId) {
        // 检查用户是否已经评价过该酒店
        if (reviewRepository.existsByUserIdAndHotelId(userId, hotelId)) {
            return false;
        }

        // 允许所有登录用户评价酒店（不需要预订记录）
        // 这是更常见的业务逻辑，用户可以基于实际体验评价酒店
        return true;

        // 如果需要严格要求用户必须有预订记录才能评价，可以启用以下代码：
        // List<Booking> completedBookings =
        // bookingRepository.findByUserIdAndHotelIdAndStatus(
        // userId, hotelId, Booking.BookingStatus.CHECKED_OUT);
        // return !completedBookings.isEmpty();
    }

    /**
     * 转换为DTO
     */
    private ReviewDto convertToDto(Review review) {
        ReviewDto dto = new ReviewDto();
        dto.setId(review.getId());
        dto.setRating(review.getRating());
        dto.setServiceRating(review.getServiceRating());
        dto.setCleanlinessRating(review.getCleanlinessRating());
        dto.setLocationRating(review.getLocationRating());
        dto.setValueRating(review.getValueRating());
        dto.setComment(review.getComment());
        dto.setReviewerName(review.getReviewerName());
        dto.setStatus(review.getStatus());
        dto.setAdminReply(review.getAdminReply());
        dto.setAdminReplyAt(review.getAdminReplyAt());
        dto.setCreatedAt(review.getCreatedAt());
        dto.setUpdatedAt(review.getUpdatedAt());
        dto.setAverageRating(review.getAverageRating());

        if (review.getUser() != null) {
            dto.setUserId(review.getUser().getId());
            dto.setUserName(review.getUser().getFullName());
        }

        if (review.getHotel() != null) {
            dto.setHotelId(review.getHotel().getId());
            dto.setHotelName(review.getHotel().getName());
        }

        if (review.getBooking() != null) {
            dto.setBookingId(review.getBooking().getId());
            dto.setBookingNumber(review.getBooking().getBookingNumber());
        }

        return dto;
    }

    /**
     * 评价统计信息DTO
     */
    public static class ReviewStatisticsDto {
        private Long hotelId;
        private Long totalReviews;
        private Double averageRating;
        private Double overallRating;
        private Double serviceRating;
        private Double cleanlinessRating;
        private Double locationRating;
        private Double valueRating;
        private List<Object[]> ratingDistribution;

        // Getters and Setters
        public Long getHotelId() {
            return hotelId;
        }

        public void setHotelId(Long hotelId) {
            this.hotelId = hotelId;
        }

        public Long getTotalReviews() {
            return totalReviews;
        }

        public void setTotalReviews(Long totalReviews) {
            this.totalReviews = totalReviews;
        }

        public Double getAverageRating() {
            return averageRating;
        }

        public void setAverageRating(Double averageRating) {
            this.averageRating = averageRating;
        }

        public Double getOverallRating() {
            return overallRating;
        }

        public void setOverallRating(Double overallRating) {
            this.overallRating = overallRating;
        }

        public Double getServiceRating() {
            return serviceRating;
        }

        public void setServiceRating(Double serviceRating) {
            this.serviceRating = serviceRating;
        }

        public Double getCleanlinessRating() {
            return cleanlinessRating;
        }

        public void setCleanlinessRating(Double cleanlinessRating) {
            this.cleanlinessRating = cleanlinessRating;
        }

        public Double getLocationRating() {
            return locationRating;
        }

        public void setLocationRating(Double locationRating) {
            this.locationRating = locationRating;
        }

        public Double getValueRating() {
            return valueRating;
        }

        public void setValueRating(Double valueRating) {
            this.valueRating = valueRating;
        }

        public List<Object[]> getRatingDistribution() {
            return ratingDistribution;
        }

        public void setRatingDistribution(List<Object[]> ratingDistribution) {
            this.ratingDistribution = ratingDistribution;
        }
    }

    /**
     * 获取所有评价（管理员功能）
     */
    @Transactional(readOnly = true)
    public List<ReviewDto> getAllReviews() {
        logger.info("获取所有评价");
        List<Review> reviews = reviewRepository.findAll();
        return reviews.stream()
                .map(this::convertToDto)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取指定日期的评价数量
     */
    @Transactional(readOnly = true)
    public int getReviewCountByDate(LocalDate date) {
        try {
            return reviewRepository.countByCreatedAtBetween(
                    date.atStartOfDay(),
                    date.atTime(23, 59, 59));
        } catch (Exception e) {
            logger.warn("获取日期 {} 的评价数量失败: {}", date, e.getMessage());
            return 0;
        }
    }

    // ==================== 仪表盘统计方法 ====================

    /**
     * 获取待审核评价数量
     */
    @Transactional(readOnly = true)
    public int getPendingReviewCount() {
        try {
            return (int) reviewRepository.countByStatus(Review.ReviewStatus.PENDING);
        } catch (Exception e) {
            logger.warn("获取待审核评价数量失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 获取平均评分
     */
    @Transactional(readOnly = true)
    public double getAverageRating() {
        try {
            List<ReviewDto> reviews = getAllReviews();
            if (reviews.isEmpty()) {
                return 0.0;
            }

            double totalRating = reviews.stream()
                    .filter(review -> review.getRating() != null)
                    .mapToDouble(ReviewDto::getRating)
                    .sum();

            long reviewCount = reviews.stream()
                    .filter(review -> review.getRating() != null)
                    .count();

            return reviewCount > 0 ? totalRating / reviewCount : 0.0;
        } catch (Exception e) {
            logger.warn("获取评价平均评分失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 获取评分分布
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getRatingDistribution() {
        try {
            List<Map<String, Object>> distribution = new ArrayList<>();

            // 统计1-5星的分布
            for (int rating = 1; rating <= 5; rating++) {
                long count = reviewRepository.countByRating(rating);
                Map<String, Object> item = new HashMap<>();
                item.put("rating", rating + "星");
                item.put("count", count);
                distribution.add(item);
            }

            return distribution;
        } catch (Exception e) {
            logger.warn("获取评分分布失败: {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取评价趋势数据
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getReviewTrends(LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> trends = new HashMap<>();
        List<String> labels = new ArrayList<>();
        List<Integer> reviews = new ArrayList<>();

        try {
            // 按天统计评价数量
            LocalDate start = startDate.toLocalDate();
            LocalDate end = endDate.toLocalDate();

            while (!start.isAfter(end)) {
                labels.add(start.toString());
                int count = getReviewCountByDate(start);
                reviews.add(count);
                start = start.plusDays(1);
            }

            trends.put("labels", labels);
            trends.put("reviews", reviews);
        } catch (Exception e) {
            logger.warn("获取评价趋势数据失败: {}", e.getMessage());
        }

        return trends;
    }
}
