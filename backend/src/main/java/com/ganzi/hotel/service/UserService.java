package com.ganzi.hotel.service;

import com.ganzi.hotel.dto.UserDto;
import com.ganzi.hotel.dto.UserRegistrationDto;
import com.ganzi.hotel.entity.User;
import com.ganzi.hotel.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户服务类
 */
@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 用户注册
     */
    public UserDto registerUser(UserRegistrationDto registrationDto) {
        logger.info("Registering new user: {}", registrationDto.getUsername());

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(registrationDto.getUsername())) {
            throw new RuntimeException("用户名已存在: " + registrationDto.getUsername());
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(registrationDto.getEmail())) {
            throw new RuntimeException("邮箱已存在: " + registrationDto.getEmail());
        }

        // 创建新用户
        User user = new User();
        user.setUsername(registrationDto.getUsername());
        user.setEmail(registrationDto.getEmail());
        user.setPassword(passwordEncoder.encode(registrationDto.getPassword()));
        user.setFullName(registrationDto.getFullName());
        user.setPhone(registrationDto.getPhone());
        user.setRole(User.Role.USER);
        user.setIsActive(true);
        user.setEmailVerified(false);

        User savedUser = userRepository.save(user);
        logger.info("Successfully registered user: {} with ID: {}", savedUser.getUsername(), savedUser.getId());

        return UserDto.fromEntity(savedUser);
    }

    /**
     * 根据用户名查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 根据邮箱查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    /**
     * 根据用户名或邮箱查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findByUsernameOrEmail(String usernameOrEmail) {
        return userRepository.findByUsernameOrEmail(usernameOrEmail);
    }

    /**
     * 根据ID查找用户
     */
    @Transactional(readOnly = true)
    public Optional<UserDto> findById(Long id) {
        return userRepository.findById(id).map(UserDto::fromEntity);
    }

    /**
     * 获取用户详情
     */
    @Transactional(readOnly = true)
    public UserDto getUserProfile(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在，ID: " + userId));
        return UserDto.fromEntity(user);
    }

    /**
     * 更新用户信息
     */
    public UserDto updateUserProfile(Long userId, UserDto userDto) {
        logger.info("Updating user profile for ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在，ID: " + userId));

        // 更新允许修改的字段
        if (userDto.getFullName() != null) {
            user.setFullName(userDto.getFullName());
        }
        if (userDto.getPhone() != null) {
            user.setPhone(userDto.getPhone());
        }
        if (userDto.getAvatarUrl() != null) {
            user.setAvatarUrl(userDto.getAvatarUrl());
        }

        User updatedUser = userRepository.save(user);
        logger.info("Successfully updated user profile for ID: {}", userId);

        return UserDto.fromEntity(updatedUser);
    }

    /**
     * 修改密码
     */
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        logger.info("Changing password for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在，ID: " + userId));

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("原密码不正确");
        }

        // 设置新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);

        logger.info("Successfully changed password for user ID: {}", userId);
    }

    /**
     * 重置用户密码（管理员功能）
     */
    public void resetUserPassword(Long userId, String newPassword) {
        logger.info("Resetting password for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在，ID: " + userId));

        // 设置新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);

        logger.info("Successfully reset password for user ID: {}", userId);
    }

    /**
     * 激活/禁用用户
     */
    public void toggleUserStatus(Long userId, boolean isActive) {
        logger.info("Toggling user status for ID: {} to {}", userId, isActive);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在，ID: " + userId));

        user.setIsActive(isActive);
        userRepository.save(user);

        logger.info("Successfully toggled user status for ID: {} to {}", userId, isActive);
    }

    /**
     * 验证邮箱
     */
    public void verifyEmail(Long userId) {
        logger.info("Verifying email for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在，ID: " + userId));

        user.setEmailVerified(true);
        userRepository.save(user);

        logger.info("Successfully verified email for user ID: {}", userId);
    }

    /**
     * 获取所有用户（管理员功能）
     */
    @Transactional(readOnly = true)
    public List<UserDto> getAllUsers() {
        return userRepository.findAll().stream()
                .map(UserDto::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 搜索用户
     */
    @Transactional(readOnly = true)
    public List<UserDto> searchUsers(String keyword) {
        return userRepository.findByUsernameOrFullNameContaining(keyword).stream()
                .map(UserDto::fromEntity)
                .collect(Collectors.toList());
    }

    /**
     * 获取活跃用户数量
     */
    @Transactional(readOnly = true)
    public long getActiveUserCount() {
        return userRepository.countActiveUsers();
    }

    /**
     * 获取管理员数量
     */
    @Transactional(readOnly = true)
    public long getAdminCount() {
        return userRepository.countActiveAdmins();
    }

    /**
     * 检查用户名是否可用
     */
    @Transactional(readOnly = true)
    public boolean isUsernameAvailable(String username) {
        return !userRepository.existsByUsername(username);
    }

    /**
     * 检查邮箱是否可用
     */
    @Transactional(readOnly = true)
    public boolean isEmailAvailable(String email) {
        return !userRepository.existsByEmail(email);
    }

    /**
     * 获取指定日期的用户数量
     */
    @Transactional(readOnly = true)
    public int getUserCountByDate(LocalDate date) {
        try {
            return userRepository.countByCreatedAtBetween(
                    date.atStartOfDay(),
                    date.atTime(23, 59, 59));
        } catch (Exception e) {
            logger.warn("获取日期 {} 的用户数量失败: {}", date, e.getMessage());
            return 0;
        }
    }

    // ==================== 仪表盘统计方法 ====================

    /**
     * 获取今日新增用户数量
     */
    @Transactional(readOnly = true)
    public int getTodayNewUserCount() {
        LocalDate today = LocalDate.now();
        return getUserCountByDate(today);
    }

    /**
     * 获取用户趋势数据
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getUserTrends(LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Object> trends = new HashMap<>();
        List<String> labels = new ArrayList<>();
        List<Integer> users = new ArrayList<>();

        try {
            // 按天统计用户注册数量
            LocalDate start = startDate.toLocalDate();
            LocalDate end = endDate.toLocalDate();

            while (!start.isAfter(end)) {
                labels.add(start.toString());
                int count = getUserCountByDate(start);
                users.add(count);
                start = start.plusDays(1);
            }

            trends.put("labels", labels);
            trends.put("users", users);
        } catch (Exception e) {
            logger.warn("获取用户趋势数据失败: {}", e.getMessage());
        }

        return trends;
    }
}
