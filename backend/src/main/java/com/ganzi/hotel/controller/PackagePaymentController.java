package com.ganzi.hotel.controller;

import com.ganzi.hotel.dto.ApiResponse;
import com.ganzi.hotel.dto.PackagePaymentDto;
import com.ganzi.hotel.dto.PackagePaymentRequestDto;
import com.ganzi.hotel.dto.PackagePaymentResponseDto;
import com.ganzi.hotel.entity.PackagePayment;
import com.ganzi.hotel.service.PackagePaymentService;
import com.ganzi.hotel.security.JwtUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 套餐付款控制器
 */
@RestController
@RequestMapping("/packages")
@CrossOrigin(origins = { "http://localhost:3001", "http://localhost:3002", "http://localhost:3003" })
public class PackagePaymentController {

    private static final Logger logger = LoggerFactory.getLogger(PackagePaymentController.class);

    @Autowired
    private PackagePaymentService paymentService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 创建套餐付款
     */
    @PostMapping("/bookings/{bookingId}/payment")
    public ResponseEntity<ApiResponse<PackagePaymentResponseDto>> createPayment(
            @PathVariable Long bookingId,
            @Valid @RequestBody PackagePaymentRequestDto requestDto,
            HttpServletRequest request) {

        logger.info("创建套餐付款，预订ID: {}, 请求: {}", bookingId, requestDto);

        try {
            // 获取用户ID
            Long userId = getUserIdFromToken(request);

            // 设置预订ID和客户端信息
            requestDto.setPackageBookingId(bookingId);
            requestDto.setClientIp(getClientIp(request));
            requestDto.setUserAgent(request.getHeader("User-Agent"));

            PackagePaymentResponseDto response = paymentService.createPayment(requestDto, userId);
            return ResponseEntity.ok(ApiResponse.success("付款创建成功", response));

        } catch (IllegalArgumentException e) {
            logger.warn("创建付款失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("创建付款失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("创建付款时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("创建付款失败: " + e.getMessage()));
        }
    }

    /**
     * 处理付款
     */
    @PostMapping("/payments/{paymentNumber}/process")
    public ResponseEntity<ApiResponse<PackagePaymentDto>> processPayment(
            @PathVariable String paymentNumber,
            HttpServletRequest request) {

        logger.info("处理套餐付款，付款号: {}", paymentNumber);

        try {
            PackagePaymentDto payment = paymentService.processPayment(paymentNumber);
            return ResponseEntity.ok(ApiResponse.success("付款处理成功", payment));

        } catch (IllegalArgumentException e) {
            logger.warn("处理付款失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("处理付款失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("处理付款时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("处理付款失败: " + e.getMessage()));
        }
    }

    /**
     * 查询付款记录
     */
    @GetMapping("/payments/{paymentNumber}")
    public ResponseEntity<ApiResponse<PackagePaymentDto>> getPayment(
            @PathVariable String paymentNumber) {

        logger.info("查询套餐付款记录，付款号: {}", paymentNumber);

        try {
            Optional<PackagePaymentDto> payment = paymentService.getPaymentByNumber(paymentNumber);
            if (payment.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("查询付款记录成功", payment.get()));
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            logger.error("查询付款记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("查询付款记录失败: " + e.getMessage()));
        }
    }

    /**
     * 获取预订的付款记录
     */
    @GetMapping("/bookings/{bookingId}/payments")
    public ResponseEntity<ApiResponse<List<PackagePaymentDto>>> getBookingPayments(
            @PathVariable Long bookingId) {

        logger.info("获取预订付款记录，预订ID: {}", bookingId);

        try {
            List<PackagePaymentDto> payments = paymentService.getPaymentsByBooking(bookingId);
            return ResponseEntity.ok(ApiResponse.success("获取预订付款记录成功", payments));

        } catch (Exception e) {
            logger.error("获取预订付款记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取预订付款记录失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户的付款记录
     */
    @GetMapping("/payments/my")
    public ResponseEntity<ApiResponse<Page<PackagePaymentDto>>> getUserPayments(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            HttpServletRequest request) {

        logger.info("获取用户付款记录，页码: {}, 大小: {}", page, size);

        try {
            Long userId = getUserIdFromToken(request);
            Pageable pageable = PageRequest.of(page, size);
            Page<PackagePaymentDto> payments = paymentService.getUserPayments(userId, pageable);
            return ResponseEntity.ok(ApiResponse.success("获取用户付款记录成功", payments));

        } catch (Exception e) {
            logger.error("获取用户付款记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取用户付款记录失败: " + e.getMessage()));
        }
    }

    /**
     * 搜索付款记录
     */
    @GetMapping("/payments/search")
    public ResponseEntity<ApiResponse<Page<PackagePaymentDto>>> searchPayments(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long packageBookingId,
            @RequestParam(required = false) PackagePayment.PaymentStatus status,
            @RequestParam(required = false) PackagePayment.PaymentMethod paymentMethod,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        logger.info("搜索付款记录，用户ID: {}, 预订ID: {}, 状态: {}, 付款方式: {}, 时间范围: {} - {}",
                userId, packageBookingId, status, paymentMethod, startTime, endTime);

        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<PackagePaymentDto> payments = paymentService.searchPayments(
                    userId, packageBookingId, status, paymentMethod, startTime, endTime, pageable);
            return ResponseEntity.ok(ApiResponse.success("搜索付款记录成功", payments));

        } catch (Exception e) {
            logger.error("搜索付款记录失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("搜索付款记录失败: " + e.getMessage()));
        }
    }

    /**
     * 取消付款
     */
    @PostMapping("/payments/{paymentNumber}/cancel")
    public ResponseEntity<ApiResponse<PackagePaymentDto>> cancelPayment(
            @PathVariable String paymentNumber,
            @RequestParam(required = false, defaultValue = "用户取消") String reason) {

        logger.info("取消套餐付款，付款号: {}, 原因: {}", paymentNumber, reason);

        try {
            PackagePaymentDto payment = paymentService.cancelPayment(paymentNumber, reason);
            return ResponseEntity.ok(ApiResponse.success("付款取消成功", payment));

        } catch (IllegalArgumentException e) {
            logger.warn("取消付款失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("取消付款失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("取消付款时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("取消付款失败: " + e.getMessage()));
        }
    }

    /**
     * 申请退款
     */
    @PostMapping("/payments/{paymentNumber}/refund")
    public ResponseEntity<ApiResponse<PackagePaymentDto>> requestRefund(
            @PathVariable String paymentNumber,
            @RequestParam BigDecimal refundAmount,
            @RequestParam String reason) {

        logger.info("申请套餐付款退款，付款号: {}, 退款金额: {}, 原因: {}", paymentNumber, refundAmount, reason);

        try {
            PackagePaymentDto payment = paymentService.requestRefund(paymentNumber, refundAmount, reason);
            return ResponseEntity.ok(ApiResponse.success("退款申请成功", payment));

        } catch (IllegalArgumentException e) {
            logger.warn("申请退款失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("申请退款失败: " + e.getMessage()));
        } catch (Exception e) {
            logger.error("申请退款时发生错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("申请退款失败: " + e.getMessage()));
        }
    }

    /**
     * 获取付款统计信息
     */
    @GetMapping("/payments/statistics")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getPaymentStatistics() {
        logger.info("获取套餐付款统计信息");

        try {
            Map<String, Object> statistics = paymentService.getPaymentStatistics();
            return ResponseEntity.ok(ApiResponse.success("获取付款统计信息成功", statistics));

        } catch (Exception e) {
            logger.error("获取付款统计信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("获取付款统计信息失败: " + e.getMessage()));
        }
    }

    /**
     * 支付回调接口（支付宝）
     */
    @PostMapping("/payments/callback/alipay")
    public ResponseEntity<String> alipayCallback(@RequestBody Map<String, String> params) {
        logger.info("收到支付宝付款回调: {}", params);

        try {
            // 验证签名和处理回调
            // 这里应该实现具体的支付宝回调处理逻辑
            return ResponseEntity.ok("success");

        } catch (Exception e) {
            logger.error("处理支付宝回调失败", e);
            return ResponseEntity.ok("fail");
        }
    }

    /**
     * 支付回调接口（微信支付）
     */
    @PostMapping("/payments/callback/wechat")
    public ResponseEntity<String> wechatCallback(@RequestBody String xmlData) {
        logger.info("收到微信支付回调: {}", xmlData);

        try {
            // 验证签名和处理回调
            // 这里应该实现具体的微信支付回调处理逻辑
            return ResponseEntity.ok("<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>");

        } catch (Exception e) {
            logger.error("处理微信支付回调失败", e);
            return ResponseEntity.ok("<xml><return_code><![CDATA[FAIL]]></return_code></xml>");
        }
    }

    // 私有辅助方法

    /**
     * 从Token获取用户ID
     */
    private Long getUserIdFromToken(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            return jwtUtil.extractUserId(token);
        }
        // 对于测试目的，如果没有令牌，返回默认用户ID（管理员用户）
        logger.warn("没有提供认证令牌，使用默认用户ID进行测试");
        return 1L; // 假设管理员用户ID为1
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
