package com.ganzi.hotel.controller;

import com.ganzi.hotel.dto.*;
import com.ganzi.hotel.entity.Room;
import com.ganzi.hotel.entity.Payment;
import com.ganzi.hotel.repository.PaymentRepository;
import com.ganzi.hotel.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/admin")
@CrossOrigin(origins = "*", maxAge = 3600)
// @PreAuthorize("hasRole('ADMIN')") // 临时注释掉用于调试
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private HotelService hotelService;

    @Autowired
    private BookingService bookingService;

    @Autowired
    private ReviewService reviewService;

    @Autowired
    private RoomService roomService;

    @Autowired
    private DataInitializationService dataInitializationService;

    @Autowired
    private PaymentRepository paymentRepository;

    /**
     * 检查管理员权限
     */
    private boolean checkAdminPermission(Authentication authentication) {
        if (authentication == null) {
            logger.warn("未认证的请求");
            return false;
        }

        boolean hasAdminRole = authentication.getAuthorities().stream()
                .anyMatch(auth -> auth.getAuthority().equals("ROLE_ADMIN"));

        if (!hasAdminRole) {
            logger.warn("用户 {} 没有管理员权限，当前权限: {}",
                    authentication.getName(), authentication.getAuthorities());
        }

        return hasAdminRole;
    }

    // ==================== 仪表盘统计 API ====================

    /**
     * 获取仪表盘统计数据
     */
    @GetMapping("/dashboard/stats")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getDashboardStats() {
        try {
            logger.info("获取仪表盘统计数据");

            Map<String, Object> stats = new HashMap<>();

            // 用户统计
            Map<String, Object> userStats = new HashMap<>();
            List<UserDto> allUsers = userService.getAllUsers();
            userStats.put("total", allUsers.size());
            userStats.put("activeUsers", userService.getActiveUserCount());
            userStats.put("adminCount", userService.getAdminCount());
            userStats.put("todayNew", userService.getTodayNewUserCount());
            stats.put("users", userStats);

            // 酒店统计
            Map<String, Object> hotelStats = new HashMap<>();
            List<HotelDto> allHotels = hotelService.getAllActiveHotels();
            hotelStats.put("total", allHotels.size());
            hotelStats.put("active", allHotels.size());
            hotelStats.put("avgRating", hotelService.getAverageRating());
            hotelStats.put("todayNew", hotelService.getTodayNewHotelCount());
            stats.put("hotels", hotelStats);

            // 预订统计
            Map<String, Object> bookingStats = new HashMap<>();
            List<BookingDto> allBookings = bookingService.getAllBookings();
            bookingStats.put("total", allBookings.size());
            bookingStats.put("todayNew", bookingService.getTodayNewBookingCount());
            bookingStats.put("totalRevenue", bookingService.getTotalRevenue());
            bookingStats.put("todayRevenue", bookingService.getTodayRevenue());
            bookingStats.put("statusDistribution", bookingService.getStatusDistribution());
            stats.put("bookings", bookingStats);

            // 评价统计
            Map<String, Object> reviewStats = new HashMap<>();
            List<ReviewDto> allReviews = reviewService.getAllReviews();
            reviewStats.put("total", allReviews.size());
            reviewStats.put("pending", reviewService.getPendingReviewCount());
            reviewStats.put("avgRating", reviewService.getAverageRating());
            reviewStats.put("ratingDistribution", reviewService.getRatingDistribution());
            stats.put("reviews", reviewStats);

            return ResponseEntity.ok(ApiResponse.success("获取统计数据成功", stats));
        } catch (Exception e) {
            logger.error("获取仪表盘统计数据失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取统计数据失败: " + e.getMessage()));
        }
    }

    /**
     * 获取趋势数据
     */
    @GetMapping("/dashboard/trends")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getTrends(
            @RequestParam(defaultValue = "7d") String period,
            @RequestParam(defaultValue = "bookings") String type) {
        try {
            logger.info("获取趋势数据，周期: {}, 类型: {}", period, type);

            Map<String, Object> trends = new HashMap<>();

            // 根据周期计算日期范围
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate;

            switch (period) {
                case "7d":
                    startDate = endDate.minusDays(7);
                    break;
                case "30d":
                    startDate = endDate.minusDays(30);
                    break;
                case "90d":
                    startDate = endDate.minusDays(90);
                    break;
                case "1y":
                    startDate = endDate.minusYears(1);
                    break;
                default:
                    startDate = endDate.minusDays(7);
            }

            // 根据类型获取趋势数据
            switch (type) {
                case "bookings":
                    trends = bookingService.getBookingTrends(startDate, endDate);
                    break;
                case "revenue":
                    trends = bookingService.getRevenueTrends(startDate, endDate);
                    break;
                case "users":
                    trends = userService.getUserTrends(startDate, endDate);
                    break;
                case "reviews":
                    trends = reviewService.getReviewTrends(startDate, endDate);
                    break;
                default:
                    trends = bookingService.getBookingTrends(startDate, endDate);
            }

            return ResponseEntity.ok(ApiResponse.success("获取趋势数据成功", trends));
        } catch (Exception e) {
            logger.error("获取趋势数据失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取趋势数据失败: " + e.getMessage()));
        }
    }

    /**
     * 调试：获取所有支付记录
     */
    @GetMapping("/debug/payments")
    public ResponseEntity<ApiResponse<List<Payment>>> getDebugPayments() {
        try {
            List<Payment> allPayments = paymentRepository.findAll();
            return ResponseEntity.ok(ApiResponse.success("获取支付记录成功", allPayments));
        } catch (Exception e) {
            logger.error("获取支付记录失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取支付记录失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户列表
     */
    @GetMapping("/users")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String status) {
        try {
            logger.info("获取用户列表，页码: {}, 大小: {}, 搜索: {}", page, size, search);

            List<UserDto> users = userService.getAllUsers();

            // 简单的分页处理
            int start = page * size;
            int end = Math.min(start + size, users.size());
            List<UserDto> pageUsers = users.subList(start, end);

            Map<String, Object> result = new HashMap<>();
            result.put("content", pageUsers);
            result.put("totalElements", users.size());
            result.put("totalPages", (int) Math.ceil((double) users.size() / size));
            result.put("size", size);
            result.put("number", page);
            result.put("first", page == 0);
            result.put("last", end >= users.size());

            return ResponseEntity.ok(ApiResponse.success("获取用户列表成功", result));
        } catch (Exception e) {
            logger.error("获取用户列表失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取用户列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/users/{id}")
    public ResponseEntity<ApiResponse<UserDto>> getUserById(@PathVariable Long id) {
        try {
            logger.info("获取用户详情，ID: {}", id);
            Optional<UserDto> user = userService.findById(id);
            if (user.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取用户详情成功", user.get()));
            } else {
                return ResponseEntity.status(404)
                        .body(ApiResponse.error("用户不存在"));
            }
        } catch (Exception e) {
            logger.error("获取用户详情失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取用户详情失败: " + e.getMessage()));
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/users/{id}")
    public ResponseEntity<ApiResponse<UserDto>> updateUser(@PathVariable Long id, @RequestBody UserDto userDto) {
        try {
            logger.info("更新用户信息，ID: {}", id);
            UserDto updatedUser = userService.updateUserProfile(id, userDto);
            return ResponseEntity.ok(ApiResponse.success("更新用户信息成功", updatedUser));
        } catch (Exception e) {
            logger.error("更新用户信息失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("更新用户信息失败: " + e.getMessage()));
        }
    }

    /**
     * 切换用户状态（启用/禁用）
     */
    @PostMapping("/users/{id}/toggle-status")
    public ResponseEntity<ApiResponse<Void>> toggleUserStatus(@PathVariable Long id) {
        try {
            logger.info("切换用户状态，ID: {}", id);
            Optional<UserDto> userOpt = userService.findById(id);
            if (userOpt.isPresent()) {
                UserDto user = userOpt.get();
                userService.toggleUserStatus(id, !user.getIsActive());
                return ResponseEntity.ok(ApiResponse.success("用户状态切换成功", null));
            } else {
                return ResponseEntity.status(404)
                        .body(ApiResponse.error("用户不存在"));
            }
        } catch (Exception e) {
            logger.error("切换用户状态失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("切换用户状态失败: " + e.getMessage()));
        }
    }

    /**
     * 重置用户密码
     */
    @PostMapping("/users/{id}/reset-password")
    public ResponseEntity<ApiResponse<Map<String, String>>> resetUserPassword(
            @PathVariable Long id,
            @RequestBody Map<String, String> request) {
        try {
            logger.info("重置用户密码，ID: {}", id);
            String newPassword = request.get("newPassword");
            if (newPassword == null || newPassword.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("新密码不能为空"));
            }

            userService.resetUserPassword(id, newPassword);

            Map<String, String> result = new HashMap<>();
            result.put("newPassword", newPassword);
            return ResponseEntity.ok(ApiResponse.success("密码重置成功", result));
        } catch (Exception e) {
            logger.error("重置用户密码失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("重置用户密码失败: " + e.getMessage()));
        }
    }

    /**
     * 获取酒店列表
     */
    @GetMapping("/hotels")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getHotels(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Integer starRating) {
        try {
            logger.info("获取酒店列表，页码: {}, 大小: {}", page, size);

            List<HotelDto> hotels = hotelService.getAllActiveHotels();

            // 简单的分页处理
            int start = page * size;
            int end = Math.min(start + size, hotels.size());
            List<HotelDto> pageHotels = hotels.subList(start, end);

            Map<String, Object> result = new HashMap<>();
            result.put("content", pageHotels);
            result.put("totalElements", hotels.size());
            result.put("totalPages", (int) Math.ceil((double) hotels.size() / size));
            result.put("size", size);
            result.put("number", page);
            result.put("first", page == 0);
            result.put("last", end >= hotels.size());

            return ResponseEntity.ok(ApiResponse.success("获取酒店列表成功", result));
        } catch (Exception e) {
            logger.error("获取酒店列表失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取酒店列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取房间列表
     */
    @GetMapping("/rooms")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getRooms(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Long hotelId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String roomType,
            @RequestParam(required = false) String search) {
        try {
            logger.info("获取房间列表，页码: {}, 大小: {}, 酒店ID: {}, 状态: {}, 搜索: {}", page, size, hotelId, status, search);

            // 获取所有房间
            List<RoomDto> allRooms = roomService.getAllRooms();

            // 应用搜索条件
            if (search != null && !search.trim().isEmpty()) {
                String searchLower = search.trim().toLowerCase();
                allRooms = allRooms.stream()
                        .filter(r -> {
                            // 搜索房间号
                            if (r.getRoomNumber() != null && r.getRoomNumber().toLowerCase().contains(searchLower)) {
                                return true;
                            }
                            // 搜索酒店名称
                            if (r.getHotel() != null && r.getHotel().getName() != null &&
                                    r.getHotel().getName().toLowerCase().contains(searchLower)) {
                                return true;
                            }
                            // 搜索房型名称
                            if (r.getRoomType() != null && r.getRoomType().getName() != null &&
                                    r.getRoomType().getName().toLowerCase().contains(searchLower)) {
                                return true;
                            }
                            return false;
                        })
                        .collect(java.util.stream.Collectors.toList());
            }

            // 应用筛选条件
            if (hotelId != null) {
                allRooms = allRooms.stream()
                        .filter(r -> hotelId.equals(r.getHotelId()))
                        .collect(java.util.stream.Collectors.toList());
            }

            if (status != null && !status.isEmpty()) {
                allRooms = allRooms.stream()
                        .filter(r -> status.equals(r.getStatus().name()))
                        .collect(java.util.stream.Collectors.toList());
            }

            // 简单的分页处理
            int start = page * size;
            int end = Math.min(start + size, allRooms.size());
            List<RoomDto> pageRooms = allRooms.subList(start, end);

            Map<String, Object> result = new HashMap<>();
            result.put("content", pageRooms);
            result.put("totalElements", allRooms.size());
            result.put("totalPages", (int) Math.ceil((double) allRooms.size() / size));
            result.put("size", size);
            result.put("number", page);
            result.put("first", page == 0);
            result.put("last", end >= allRooms.size());

            return ResponseEntity.ok(ApiResponse.success("获取房间列表成功", result));
        } catch (Exception e) {
            logger.error("获取房间列表失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取房间列表失败: " + e.getMessage()));
        }
    }

    /**
     * 批量更新房间状态
     */
    @PostMapping("/rooms/batch-status")
    public ResponseEntity<ApiResponse<String>> batchUpdateRoomStatus(
            @RequestBody Map<String, Object> requestData) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> roomIds = (List<Integer>) requestData.get("roomIds");
            String status = (String) requestData.get("status");

            logger.info("批量更新房间状态，房间数量: {}, 状态: {}", roomIds.size(), status);

            // 转换房间状态
            Room.RoomStatus roomStatus;
            try {
                roomStatus = Room.RoomStatus.valueOf(status);
            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error("无效的房间状态: " + status));
            }

            // 批量更新房间状态
            int updatedCount = 0;
            for (Integer roomId : roomIds) {
                try {
                    Optional<RoomDto> result = roomService.updateRoomStatus(roomId.longValue(), roomStatus);
                    if (result.isPresent()) {
                        updatedCount++;
                    }
                } catch (Exception e) {
                    logger.warn("更新房间状态失败，房间ID: {}, 错误: {}", roomId, e.getMessage());
                }
            }

            return ResponseEntity.ok(ApiResponse.success(
                    String.format("成功更新 %d 个房间状态", updatedCount),
                    "批量更新完成"));
        } catch (Exception e) {
            logger.error("批量更新房间状态失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("批量更新房间状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取预订列表
     */
    @GetMapping("/bookings")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getBookings(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long hotelId,
            @RequestParam(required = false) String dateFrom,
            @RequestParam(required = false) String dateTo,
            Authentication authentication) {
        try {
            logger.info("获取预订列表，页码: {}, 大小: {}, 状态: {}, 酒店ID: {}", page, size, status, hotelId);
            logger.info("当前认证用户: {}, 权限: {}",
                    authentication != null ? authentication.getName() : "未认证",
                    authentication != null ? authentication.getAuthorities() : "无权限");

            // 临时跳过权限检查用于调试
            // if (!checkAdminPermission(authentication)) {
            // return ResponseEntity.status(403)
            // .body(ApiResponse.error("权限不足，需要管理员权限"));
            // }

            List<BookingDto> allBookings = bookingService.getAllBookings();

            // 应用筛选条件
            if (status != null && !status.isEmpty()) {
                allBookings = allBookings.stream()
                        .filter(b -> status.equals(b.getStatus()))
                        .collect(java.util.stream.Collectors.toList());
            }

            if (hotelId != null) {
                allBookings = allBookings.stream()
                        .filter(b -> hotelId.equals(b.getHotelId()))
                        .collect(java.util.stream.Collectors.toList());
            }

            // 日期范围筛选
            if (dateFrom != null && !dateFrom.isEmpty()) {
                LocalDate fromDate = LocalDate.parse(dateFrom);
                allBookings = allBookings.stream()
                        .filter(b -> b.getCheckInDate() != null && !b.getCheckInDate().isBefore(fromDate))
                        .collect(java.util.stream.Collectors.toList());
            }

            if (dateTo != null && !dateTo.isEmpty()) {
                LocalDate toDate = LocalDate.parse(dateTo);
                allBookings = allBookings.stream()
                        .filter(b -> b.getCheckOutDate() != null && !b.getCheckOutDate().isAfter(toDate))
                        .collect(java.util.stream.Collectors.toList());
            }

            // 分页处理
            int start = page * size;
            int end = Math.min(start + size, allBookings.size());
            List<BookingDto> pageBookings = allBookings.subList(start, end);

            Map<String, Object> result = new HashMap<>();
            result.put("content", pageBookings);
            result.put("totalElements", allBookings.size());
            result.put("totalPages", (int) Math.ceil((double) allBookings.size() / size));
            result.put("size", size);
            result.put("number", page);
            result.put("first", page == 0);
            result.put("last", end >= allBookings.size());

            return ResponseEntity.ok(ApiResponse.success("获取预订列表成功", result));
        } catch (Exception e) {
            logger.error("获取预订列表失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取预订列表失败: " + e.getMessage()));
        }
    }

    /**
     * 获取评价列表
     */
    @GetMapping("/reviews")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getReviews(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long hotelId,
            @RequestParam(required = false) Integer rating) {
        try {
            logger.info("获取评价列表，页码: {}, 大小: {}", page, size);

            List<ReviewDto> allReviews = reviewService.getAllReviews();

            // 简单的筛选
            if (status != null && !status.isEmpty()) {
                allReviews = allReviews.stream()
                        .filter(r -> status.equals(r.getStatus()))
                        .collect(java.util.stream.Collectors.toList());
            }

            if (hotelId != null) {
                allReviews = allReviews.stream()
                        .filter(r -> hotelId.equals(r.getHotelId()))
                        .collect(java.util.stream.Collectors.toList());
            }

            if (rating != null) {
                allReviews = allReviews.stream()
                        .filter(r -> rating.equals(r.getRating()))
                        .collect(java.util.stream.Collectors.toList());
            }

            // 简单的分页处理
            int start = page * size;
            int end = Math.min(start + size, allReviews.size());
            List<ReviewDto> pageReviews = allReviews.subList(start, end);

            Map<String, Object> result = new HashMap<>();
            result.put("content", pageReviews);
            result.put("totalElements", allReviews.size());
            result.put("totalPages", (int) Math.ceil((double) allReviews.size() / size));
            result.put("size", size);
            result.put("number", page);
            result.put("first", page == 0);
            result.put("last", end >= allReviews.size());

            return ResponseEntity.ok(ApiResponse.success("获取评价列表成功", result));
        } catch (Exception e) {
            logger.error("获取评价列表失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取评价列表失败: " + e.getMessage()));
        }
    }

    /**
     * 审核评价
     */
    @PostMapping("/reviews/{id}/approve")
    public ResponseEntity<ApiResponse<ReviewDto>> approveReview(
            @PathVariable Long id,
            @RequestParam boolean approved,
            @RequestParam(required = false) String reason) {
        try {
            logger.info("审核评价，ID: {}, 通过: {}", id, approved);

            ReviewDto reviewDto = reviewService.approveReview(id, approved, reason);
            return ResponseEntity.ok(ApiResponse.success("评价审核成功", reviewDto));
        } catch (Exception e) {
            logger.error("评价审核失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("评价审核失败: " + e.getMessage()));
        }
    }

    /**
     * 获取预订详情
     */
    @GetMapping("/bookings/{id}")
    public ResponseEntity<ApiResponse<BookingDto>> getBookingDetail(@PathVariable Long id,
            Authentication authentication) {
        try {
            logger.info("获取预订详情，ID: {}", id);
            logger.info("当前认证用户: {}, 权限: {}",
                    authentication != null ? authentication.getName() : "未认证",
                    authentication != null ? authentication.getAuthorities() : "无权限");

            // 临时跳过权限检查用于调试
            // if (!checkAdminPermission(authentication)) {
            // return ResponseEntity.status(403)
            // .body(ApiResponse.error("权限不足，需要管理员权限"));
            // }

            Optional<BookingDto> bookingOpt = bookingService.getBookingById(id);
            if (bookingOpt.isPresent()) {
                return ResponseEntity.ok(ApiResponse.success("获取预订详情成功", bookingOpt.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("获取预订详情失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("获取预订详情失败: " + e.getMessage()));
        }
    }

    /**
     * 预订操作（确认、取消、入住、退房等）
     */
    @PostMapping("/bookings/{id}/action")
    public ResponseEntity<ApiResponse<BookingDto>> operateBooking(
            @PathVariable Long id,
            @RequestParam String action,
            @RequestParam(required = false) String reason,
            @RequestParam(required = false) Double refundAmount) {
        try {
            logger.info("预订操作，ID: {}, 操作: {}, 原因: {}", id, action, reason);

            BookingDto bookingDto;
            switch (action.toLowerCase()) {
                case "confirm":
                    bookingDto = bookingService.confirmBooking(id);
                    break;
                case "cancel":
                    bookingDto = bookingService.cancelBooking(id, reason);
                    break;
                case "checkin":
                    bookingDto = bookingService.checkIn(id, null); // 房间ID可以为null，系统自动分配
                    break;
                case "checkout":
                    bookingDto = bookingService.checkOut(id);
                    break;
                case "modify":
                    // 处理退款操作
                    BigDecimal refundAmountBD = refundAmount != null ? BigDecimal.valueOf(refundAmount) : null;
                    bookingDto = bookingService.processRefund(id, refundAmountBD, reason);
                    break;
                default:
                    return ResponseEntity.badRequest()
                            .body(ApiResponse.error("不支持的操作类型: " + action));
            }

            return ResponseEntity.ok(ApiResponse.success("预订操作成功", bookingDto));
        } catch (IllegalArgumentException e) {
            // 业务逻辑错误，返回200状态码但success=false
            logger.warn("预订操作业务逻辑错误: {}", e.getMessage());
            return ResponseEntity.ok(ApiResponse.error("预订操作失败: " + e.getMessage()));
        } catch (Exception e) {
            // 系统错误，返回500状态码
            logger.error("预订操作系统错误", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("预订操作失败: " + e.getMessage()));
        }
    }

    /**
     * 管理员回复评价
     */
    @PostMapping("/reviews/{id}/reply")
    public ResponseEntity<ApiResponse<ReviewDto>> replyToReview(
            @PathVariable Long id,
            @RequestParam String reply) {
        try {
            logger.info("管理员回复评价，ID: {}", id);

            ReviewDto reviewDto = reviewService.replyToReview(id, reply);
            return ResponseEntity.ok(ApiResponse.success("回复成功", reviewDto));
        } catch (Exception e) {
            logger.error("回复评价失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("回复评价失败: " + e.getMessage()));
        }
    }

    /**
     * 隐藏评价
     */
    @PostMapping("/reviews/{id}/hide")
    public ResponseEntity<ApiResponse<ReviewDto>> hideReview(
            @PathVariable Long id,
            @RequestParam(required = false) String reason) {
        try {
            logger.info("隐藏评价，ID: {}", id);

            ReviewDto reviewDto = reviewService.hideReview(id, reason);
            return ResponseEntity.ok(ApiResponse.success("评价隐藏成功", reviewDto));
        } catch (Exception e) {
            logger.error("隐藏评价失败", e);
            return ResponseEntity.status(500)
                    .body(ApiResponse.error("隐藏评价失败: " + e.getMessage()));
        }
    }

    /**
     * 清理所有业务数据
     */
    @PostMapping("/clear-data")
    public ResponseEntity<ApiResponse<String>> clearAllData() {
        logger.info("管理员请求清理所有业务数据");

        try {
            dataInitializationService.clearAllBusinessData();

            return ResponseEntity.ok(ApiResponse.success(
                    "所有业务数据已清理完成",
                    "数据清理成功"));

        } catch (Exception e) {
            logger.error("清理数据失败", e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponse.error("清理数据失败: " + e.getMessage()));
        }
    }

}
