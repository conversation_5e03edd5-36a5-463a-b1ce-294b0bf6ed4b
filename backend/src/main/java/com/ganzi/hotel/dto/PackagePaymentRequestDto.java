package com.ganzi.hotel.dto;

import com.ganzi.hotel.entity.PackagePayment;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 套餐付款请求DTO
 */
public class PackagePaymentRequestDto {

    private Long packageBookingId;

    @NotNull(message = "付款金额不能为空")
    @DecimalMin(value = "0.01", message = "付款金额必须大于0")
    private BigDecimal amount;

    private String currency = "CNY";

    @NotNull(message = "付款方式不能为空")
    private PackagePayment.PaymentMethod paymentMethod;

    private String gatewayType;
    private String clientIp;
    private String userAgent;
    private String returnUrl;
    private String notifyUrl;
    private LocalDateTime expiresAt;
    private String remarks;

    // 支付宝相关参数
    private String alipaySubject;
    private String alipayBody;

    // 微信支付相关参数
    private String wechatBody;
    private String wechatAttach;

    // 银行卡相关参数
    private String cardNumber;
    private String cardHolderName;
    private String expiryMonth;
    private String expiryYear;
    private String cvv;

    // 构造函数
    public PackagePaymentRequestDto() {
    }

    // Getters and Setters
    public Long getPackageBookingId() {
        return packageBookingId;
    }

    public void setPackageBookingId(Long packageBookingId) {
        this.packageBookingId = packageBookingId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public PackagePayment.PaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(PackagePayment.PaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getGatewayType() {
        return gatewayType;
    }

    public void setGatewayType(String gatewayType) {
        this.gatewayType = gatewayType;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getReturnUrl() {
        return returnUrl;
    }

    public void setReturnUrl(String returnUrl) {
        this.returnUrl = returnUrl;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getAlipaySubject() {
        return alipaySubject;
    }

    public void setAlipaySubject(String alipaySubject) {
        this.alipaySubject = alipaySubject;
    }

    public String getAlipayBody() {
        return alipayBody;
    }

    public void setAlipayBody(String alipayBody) {
        this.alipayBody = alipayBody;
    }

    public String getWechatBody() {
        return wechatBody;
    }

    public void setWechatBody(String wechatBody) {
        this.wechatBody = wechatBody;
    }

    public String getWechatAttach() {
        return wechatAttach;
    }

    public void setWechatAttach(String wechatAttach) {
        this.wechatAttach = wechatAttach;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getCardHolderName() {
        return cardHolderName;
    }

    public void setCardHolderName(String cardHolderName) {
        this.cardHolderName = cardHolderName;
    }

    public String getExpiryMonth() {
        return expiryMonth;
    }

    public void setExpiryMonth(String expiryMonth) {
        this.expiryMonth = expiryMonth;
    }

    public String getExpiryYear() {
        return expiryYear;
    }

    public void setExpiryYear(String expiryYear) {
        this.expiryYear = expiryYear;
    }

    public String getCvv() {
        return cvv;
    }

    public void setCvv(String cvv) {
        this.cvv = cvv;
    }

    @Override
    public String toString() {
        return "PackagePaymentRequestDto{" +
                "packageBookingId=" + packageBookingId +
                ", amount=" + amount +
                ", currency='" + currency + '\'' +
                ", paymentMethod=" + paymentMethod +
                ", gatewayType='" + gatewayType + '\'' +
                ", clientIp='" + clientIp + '\'' +
                ", expiresAt=" + expiresAt +
                '}';
    }
}
