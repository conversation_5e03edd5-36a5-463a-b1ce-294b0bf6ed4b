# 甘孜州酒店管理系统 - 系统文档

## 📋 系统概述

甘孜州酒店管理系统是一个专为甘孜藏族自治州地区设计的综合性酒店预订和管理平台。系统包含用户端预订系统和管理员后台系统，提供完整的酒店运营管理解决方案。

### 🎯 系统目标

- **提升管理效率**: 通过数字化管理提升酒店运营效率
- **优化用户体验**: 为游客提供便捷的预订和服务体验
- **文化传承**: 结合藏族文化特色，推广当地文化旅游
- **数据驱动**: 通过数据分析支持经营决策

## 🏗️ 系统架构

### 技术栈

**前端技术栈**

- React 18.2.0 - 现代化前端框架
- TypeScript 5.2.2 - 类型安全的JavaScript
- Ant Design 5.12.8 - 企业级UI组件库
- Vite 5.0.8 - 快速构建工具
- React Router 6.20.1 - 客户端路由
- Axios 1.6.2 - HTTP客户端
- @ant-design/charts 2.6.0 - 数据可视化

**后端技术栈**

- Spring Boot 3.x - Java企业级框架
- Spring Security - 安全框架
- Spring Data JPA - 数据访问层
- MySQL 8.0 - 关系型数据库
- JWT - 身份认证
- Maven - 项目管理工具

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户前端      │    │   管理员后台    │    │   移动端应用    │
│   (React)       │    │   (React)       │    │   (Future)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (Spring Boot) │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   业务服务层    │
                    │   (Services)    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   数据访问层    │
                    │   (JPA/Hibernate)│
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL数据库   │
                    └─────────────────┘
```

## 📱 功能模块

### 管理员后台功能

#### 1. 认证与权限管理

- **登录认证**: JWT Token认证机制
- **角色管理**: 管理员(ADMIN)和普通用户(USER)角色
- **权限控制**: 基于角色的访问控制(RBAC)
- **会话管理**: 自动登录和会话超时处理

#### 2. 仪表板统计

- **实时数据**: 用户数、酒店数、预订数、评价数统计
- **趋势分析**: 7天/30天/90天/1年的数据趋势图表
- **状态分布**: 预订状态、房间状态等分布图
- **性能监控**: 系统性能指标监控

#### 3. 酒店管理

- **酒店信息**: 完整的酒店信息CRUD操作
- **星级管理**: 1-5星级评定系统
- **设施管理**: 酒店设施和服务项目管理
- **图片管理**: 酒店图片上传和管理
- **状态控制**: 酒店启用/停用状态管理

#### 4. 房间管理

- **房间信息**: 房间类型、价格、容量等信息管理
- **状态管理**: 可用、已占用、维护中、已预订状态
- **批量操作**: 批量更新房间状态
- **价格管理**: 房间价格设置和调整

#### 5. 预订管理

- **订单处理**: 预订确认、取消、入住、退房操作
- **状态跟踪**: 待确认、已确认、已入住、已退房、已取消
- **客户信息**: 预订客户信息查看和管理
- **收入统计**: 预订收入统计和分析

#### 6. 用户管理

- **用户信息**: 用户基本信息查看和编辑
- **角色管理**: 用户角色设置
- **状态管理**: 用户账户启用/禁用
- **活动监控**: 用户活动日志查看

#### 7. 评价管理

- **评价审核**: 待审核评价的审批流程
- **管理员回复**: 对用户评价进行官方回复
- **评价隐藏**: 不当评价的隐藏处理
- **评分统计**: 酒店评分统计和分析

#### 8. 文化套餐管理

- **套餐信息**: 文化体验套餐的完整信息管理
- **分类管理**: 藏族文化、宗教体验、自然风光等分类
- **难度等级**: 简单、中等、困难、极限难度设置
- **价格管理**: 套餐价格和优惠管理

## 🔧 开发指南

### 项目结构

```
admin-frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 通用组件
│   │   ├── AdminTable/     # 管理表格组件
│   │   ├── ErrorBoundary/  # 错误边界组件
│   │   ├── Loading/        # 加载组件
│   │   └── FeedbackEnhancer/ # 反馈组件
│   ├── hooks/              # 自定义Hooks
│   │   ├── useAuth.ts      # 认证Hook
│   │   ├── useTable.ts     # 表格数据Hook
│   │   ├── useFormValidation.ts # 表单验证Hook
│   │   └── useResponsive.ts # 响应式Hook
│   ├── layouts/            # 布局组件
│   │   └── AdminLayout/    # 管理员布局
│   ├── pages/              # 页面组件
│   │   ├── Dashboard/      # 仪表板
│   │   ├── Hotels/         # 酒店管理
│   │   ├── Rooms/          # 房间管理
│   │   ├── Bookings/       # 预订管理
│   │   ├── Users/          # 用户管理
│   │   ├── Reviews/        # 评价管理
│   │   ├── Packages/       # 文化套餐管理
│   │   └── Settings/       # 系统设置
│   ├── services/           # API服务
│   │   ├── api.ts          # 基础API配置
│   │   ├── auth.ts         # 认证API
│   │   ├── admin.ts        # 管理员API
│   │   └── mockApi.ts      # 模拟API
│   ├── styles/             # 样式文件
│   │   └── responsive.css  # 响应式样式
│   ├── types/              # TypeScript类型定义
│   ├── utils/              # 工具函数
│   └── tests/              # 测试文件
├── package.json            # 项目配置
├── vite.config.ts          # Vite配置
└── tsconfig.json           # TypeScript配置
```

### 核心组件说明

#### AdminTable 组件

通用的管理表格组件，提供以下功能：

- 数据展示和分页
- 搜索和筛选
- 批量操作
- 导出功能
- 响应式设计

#### useAuth Hook

认证状态管理Hook，提供：

- 登录/登出功能
- 用户信息管理
- Token管理
- 权限检查

#### useTable Hook

表格数据管理Hook，提供：

- 数据加载和分页
- 搜索和筛选
- 排序功能
- 选择状态管理

### API设计规范

#### 请求格式

```typescript
// 分页请求参数
interface PaginationParams {
  page: number; // 页码，从0开始
  size: number; // 每页大小
  sort?: string; // 排序字段
  direction?: "ASC" | "DESC"; // 排序方向
}

// 统一响应格式
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  timestamp: string;
}

// 分页响应格式
interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}
```

#### 错误处理

系统采用统一的错误处理机制：

- HTTP状态码标识错误类型
- 统一的错误响应格式
- 前端统一错误提示
- 错误日志记录

### 测试策略

#### 功能测试

- 使用自定义测试套件验证核心功能
- 模拟API响应测试
- 表单验证测试
- 用户交互测试

#### 性能测试

- 页面加载性能测试
- 大数据量表格性能测试
- 内存泄漏检测
- 网络请求优化验证

## 🚀 部署指南

### 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问地址
http://localhost:3001
```

### 生产环境

```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

### 环境配置

- 开发环境：自动代理到后端服务
- 生产环境：需要配置正确的API地址
- 模拟模式：无需后端服务，使用模拟数据

## 📈 性能优化

### 前端优化

- **代码分割**: 页面组件懒加载
- **资源优化**: 图片压缩和CDN加速
- **缓存策略**: 合理的浏览器缓存配置
- **Bundle优化**: Tree shaking和代码压缩

### 用户体验优化

- **响应式设计**: 支持桌面端、平板端、移动端
- **加载优化**: 骨架屏和加载状态提示
- **错误处理**: 友好的错误提示和恢复机制
- **无障碍支持**: 符合WCAG标准的无障碍设计

## 🔒 安全考虑

### 前端安全

- **XSS防护**: 输入验证和输出编码
- **CSRF防护**: Token验证机制
- **敏感信息**: 避免在前端存储敏感数据
- **HTTPS**: 强制使用HTTPS协议

### 后端安全

- **身份认证**: JWT Token认证
- **权限控制**: 基于角色的访问控制
- **数据验证**: 服务端数据验证
- **SQL注入防护**: 使用参数化查询

## 📞 技术支持

### 开发团队

- **前端开发**: React + TypeScript技术栈
- **后端开发**: Spring Boot + MySQL技术栈
- **UI/UX设计**: Ant Design设计语言
- **测试**: 功能测试和性能测试

### 联系方式

- **技术支持**: <EMAIL>
- **问题反馈**: <EMAIL>
- **紧急联系**: 400-123-4567

---

_本文档最后更新时间：2024年12月_
