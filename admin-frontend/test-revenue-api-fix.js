// 测试收入统计API修复
console.log('🧪 测试收入统计API修复...');

// 模拟API响应结构
const mockApiResponse = {
  success: true,
  message: "获取收入占比统计成功",
  data: {
    packageRevenue: {
      total: 0.00,
      breakdown: [],
      percentage: 0.00
    },
    hotelRevenue: {
      total: 10780.00,
      breakdown: [
        {
          revenue: 5676.00,
          percentage: 52.65,
          hotelId: 9,
          hotelName: "康定情歌大酒店"
        },
        {
          revenue: 3344.00,
          percentage: 31.02,
          hotelId: 10,
          hotelName: "稻城亚丁香格里拉大酒店"
        },
        {
          revenue: 1760.00,
          percentage: 16.33,
          hotelId: 18,
          hotelName: "1231231231231"
        }
      ],
      percentage: 100.00
    },
    totalRevenue: 10780.00
  },
  timestamp: "2025-09-07T01:14:23.780635"
};

// 模拟修复前的错误逻辑
function oldLogic(response) {
  console.log('❌ 修复前的逻辑:');
  console.log('response:', response);
  console.log('response.data:', response.data);
  console.log('response.data.success:', response.data && response.data.success);
  
  if (response.data && response.data.success) {
    console.log('✅ 条件满足，返回:', response.data.data);
    return response.data.data;
  } else {
    console.log('❌ 条件不满足，抛出错误');
    throw new Error('获取收入统计失败');
  }
}

// 模拟修复后的正确逻辑
function newLogic(response) {
  console.log('✅ 修复后的逻辑:');
  console.log('response:', response);
  console.log('response.success:', response && response.success);
  
  if (response && response.success) {
    console.log('✅ 条件满足，返回:', response.data);
    return response.data;
  } else {
    console.log('❌ 条件不满足，抛出错误');
    throw new Error('获取收入统计失败');
  }
}

// 测试修复前的逻辑
console.log('\n🔍 测试修复前的逻辑:');
try {
  const result1 = oldLogic(mockApiResponse);
  console.log('❌ 意外成功:', result1);
} catch (error) {
  console.log('✅ 预期的错误:', error.message);
}

// 测试修复后的逻辑
console.log('\n🔍 测试修复后的逻辑:');
try {
  const result2 = newLogic(mockApiResponse);
  console.log('✅ 成功获取数据:', {
    totalRevenue: result2.totalRevenue,
    hotelCount: result2.hotelRevenue.breakdown.length,
    packageCount: result2.packageRevenue.breakdown.length
  });
} catch (error) {
  console.log('❌ 意外错误:', error.message);
}

console.log('\n🎉 测试完成！修复验证成功！');

// 问题分析
console.log('\n📋 问题分析:');
console.log('1. 问题原因: request.get() 已经通过 .then((res) => res.data) 提取了响应数据');
console.log('2. 修复前: 错误地访问 response.data.success (实际是 response.success)');
console.log('3. 修复后: 正确地访问 response.success 和 response.data');
console.log('4. 结果: API调用成功，数据正确解析和返回');
