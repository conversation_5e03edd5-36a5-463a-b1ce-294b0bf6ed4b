<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收入统计页面测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e8e8e8;
        }
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        .stat-card {
            background: #fafafa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e8e8e8;
        }
        .stat-title {
            font-size: 14px;
            color: #8c8c8c;
            margin-bottom: 8px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
        }
        .stat-extra {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 4px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #8c8c8c;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 16px;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }
        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e8e8e8;
        }
        .table th {
            background: #fafafa;
            font-weight: 600;
        }
        .section {
            margin-bottom: 32px;
        }
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #262626;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">📊 收入占比统计</h1>
            <button class="btn" onclick="loadData()">🔄 刷新数据</button>
        </div>

        <div id="loading" class="loading">
            正在加载收入统计数据...
        </div>

        <div id="error" class="error" style="display: none;">
            <strong>加载失败：</strong>
            <span id="error-message"></span>
            <button class="btn" onclick="loadData()" style="margin-left: 10px;">重试</button>
        </div>

        <div id="content" style="display: none;">
            <!-- 总体统计 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-title">💰 总收入</div>
                    <div class="stat-value" id="total-revenue">¥0.00</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🏨 酒店收入</div>
                    <div class="stat-value" id="hotel-revenue">¥0.00</div>
                    <div class="stat-extra" id="hotel-percentage">占比: 0%</div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">🎁 套餐收入</div>
                    <div class="stat-value" id="package-revenue">¥0.00</div>
                    <div class="stat-extra" id="package-percentage">占比: 0%</div>
                </div>
            </div>

            <!-- 酒店收入排行 -->
            <div class="section">
                <h2 class="section-title">🏆 酒店收入排行</h2>
                <table class="table" id="hotel-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>酒店名称</th>
                            <th>收入金额</th>
                            <th>占比</th>
                        </tr>
                    </thead>
                    <tbody id="hotel-tbody">
                    </tbody>
                </table>
            </div>

            <!-- 套餐收入统计 -->
            <div class="section">
                <h2 class="section-title">📦 套餐收入统计</h2>
                <div id="package-empty" style="text-align: center; padding: 40px; color: #8c8c8c;">
                    暂无套餐收入数据
                </div>
                <table class="table" id="package-table" style="display: none;">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>套餐名称</th>
                            <th>收入金额</th>
                            <th>占比</th>
                        </tr>
                    </thead>
                    <tbody id="package-tbody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 格式化金额
        function formatCurrency(amount) {
            return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
        }

        // 加载数据
        async function loadData() {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const content = document.getElementById('content');
            
            loading.style.display = 'block';
            error.style.display = 'none';
            content.style.display = 'none';

            try {
                // 获取token（从localStorage或cookie）
                const token = localStorage.getItem('token') || 'eyJhbGciOiJIUzI1NiJ9.eyJmdWxsTmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsInVzZXJJZCI6MSwicm9sZSI6IkFETUlOIiwic3ViIjoiYWRtaW4iLCJpYXQiOjE3NTcxNzgzNzUsImV4cCI6MTc1NzI2NDc3NX0.uXxGfBXKfT1q4VNAAb6GgU_Z59s2eL0nydjhf6qTalM';
                
                const response = await fetch('/api/admin/revenue/breakdown', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || '获取数据失败');
                }

                const data = result.data;
                
                // 更新总体统计
                document.getElementById('total-revenue').textContent = formatCurrency(data.totalRevenue);
                document.getElementById('hotel-revenue').textContent = formatCurrency(data.hotelRevenue.total);
                document.getElementById('hotel-percentage').textContent = `占比: ${data.hotelRevenue.percentage.toFixed(2)}%`;
                document.getElementById('package-revenue').textContent = formatCurrency(data.packageRevenue.total);
                document.getElementById('package-percentage').textContent = `占比: ${data.packageRevenue.percentage.toFixed(2)}%`;

                // 更新酒店收入表格
                const hotelTbody = document.getElementById('hotel-tbody');
                hotelTbody.innerHTML = '';
                data.hotelRevenue.breakdown.forEach((hotel, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${index + 1}</td>
                        <td>${hotel.hotelName}</td>
                        <td>${formatCurrency(hotel.revenue)}</td>
                        <td>${hotel.percentage.toFixed(2)}%</td>
                    `;
                    hotelTbody.appendChild(row);
                });

                // 更新套餐收入表格
                const packageEmpty = document.getElementById('package-empty');
                const packageTable = document.getElementById('package-table');
                const packageTbody = document.getElementById('package-tbody');
                
                if (data.packageRevenue.breakdown.length > 0) {
                    packageEmpty.style.display = 'none';
                    packageTable.style.display = 'table';
                    packageTbody.innerHTML = '';
                    data.packageRevenue.breakdown.forEach((pkg, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${pkg.packageName}</td>
                            <td>${formatCurrency(pkg.revenue)}</td>
                            <td>${pkg.percentage.toFixed(2)}%</td>
                        `;
                        packageTbody.appendChild(row);
                    });
                } else {
                    packageEmpty.style.display = 'block';
                    packageTable.style.display = 'none';
                }

                loading.style.display = 'none';
                content.style.display = 'block';

            } catch (err) {
                console.error('加载收入统计数据失败:', err);
                document.getElementById('error-message').textContent = err.message;
                loading.style.display = 'none';
                error.style.display = 'block';
            }
        }

        // 页面加载时自动加载数据
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
