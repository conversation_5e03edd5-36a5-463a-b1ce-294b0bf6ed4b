// 收入占比统计页面

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Spin,
  Alert,
  Typography,
  Space,
  Button,
  Divider,
} from 'antd';
import {
  DollarOutlined,
  HomeOutlined,
  GiftOutlined,
  ReloadOutlined,
  Pie<PERSON>hartOutlined,
} from '@ant-design/icons';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { revenueApi, RevenueBreakdownData, HotelRevenueItem, PackageRevenueItem } from '@/services/revenue';

const { Title, Text } = Typography;

// 颜色配置
const COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16'];

const RevenueStatistics: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<RevenueBreakdownData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await revenueApi.getRevenueBreakdown();
      setData(result);
    } catch (err: any) {
      setError(err.message || '加载数据失败');
      console.error('加载收入统计数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // 格式化金额
  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  };

  // 准备饼图数据
  const preparePieData = () => {
    if (!data) return [];
    
    return [
      {
        name: '酒店收入',
        value: data.hotelRevenue.total,
        percentage: data.hotelRevenue.percentage,
      },
      {
        name: '套餐收入',
        value: data.packageRevenue.total,
        percentage: data.packageRevenue.percentage,
      },
    ].filter(item => item.value > 0);
  };

  // 准备酒店收入柱状图数据
  const prepareHotelBarData = () => {
    if (!data?.hotelRevenue.breakdown) return [];
    
    return data.hotelRevenue.breakdown.map(item => ({
      name: item.hotelName.length > 10 ? item.hotelName.substring(0, 10) + '...' : item.hotelName,
      fullName: item.hotelName,
      revenue: item.revenue,
      percentage: item.percentage,
    }));
  };

  // 酒店收入表格列配置
  const hotelColumns = [
    {
      title: '排名',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '酒店名称',
      dataIndex: 'hotelName',
      key: 'hotelName',
      ellipsis: true,
    },
    {
      title: '收入金额',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (value: number) => formatCurrency(value),
      sorter: (a: HotelRevenueItem, b: HotelRevenueItem) => a.revenue - b.revenue,
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (value: number) => `${value.toFixed(2)}%`,
      sorter: (a: HotelRevenueItem, b: HotelRevenueItem) => a.percentage - b.percentage,
    },
  ];

  // 套餐收入表格列配置
  const packageColumns = [
    {
      title: '排名',
      dataIndex: 'index',
      key: 'index',
      width: 60,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '套餐名称',
      dataIndex: 'packageName',
      key: 'packageName',
      ellipsis: true,
    },
    {
      title: '收入金额',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (value: number) => formatCurrency(value),
      sorter: (a: PackageRevenueItem, b: PackageRevenueItem) => a.revenue - b.revenue,
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (value: number) => `${value.toFixed(2)}%`,
      sorter: (a: PackageRevenueItem, b: PackageRevenueItem) => a.percentage - b.percentage,
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" tip="加载收入统计数据中..." />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={loadData}>
            重试
          </Button>
        }
      />
    );
  }

  if (!data) {
    return (
      <Alert
        message="暂无数据"
        description="暂时没有收入统计数据"
        type="info"
        showIcon
      />
    );
  }

  const pieData = preparePieData();
  const hotelBarData = prepareHotelBarData();

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2} style={{ margin: 0 }}>
          <PieChartOutlined style={{ marginRight: '8px' }} />
          收入占比统计
        </Title>
        <Button 
          type="primary" 
          icon={<ReloadOutlined />} 
          onClick={loadData}
          loading={loading}
        >
          刷新数据
        </Button>
      </div>

      {/* 总体统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="总收入"
              value={data.totalRevenue}
              formatter={(value) => formatCurrency(Number(value))}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="酒店收入"
              value={data.hotelRevenue.total}
              formatter={(value) => formatCurrency(Number(value))}
              prefix={<HomeOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <Text type="secondary">
              占比: {data.hotelRevenue.percentage.toFixed(2)}%
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="套餐收入"
              value={data.packageRevenue.total}
              formatter={(value) => formatCurrency(Number(value))}
              prefix={<GiftOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
            <Text type="secondary">
              占比: {data.packageRevenue.percentage.toFixed(2)}%
            </Text>
          </Card>
        </Col>
      </Row>

      {/* 收入占比饼图 */}
      {pieData.length > 0 && (
        <Card title="收入占比分布" style={{ marginBottom: '24px' }}>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percentage }) => `${name}: ${percentage.toFixed(2)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => formatCurrency(Number(value))} />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      )}

      <Row gutter={[16, 16]}>
        {/* 酒店收入统计 */}
        <Col xs={24} lg={12}>
          <Card title="酒店收入排行" style={{ height: '100%' }}>
            {hotelBarData.length > 0 && (
              <>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={hotelBarData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => `¥${(value / 1000).toFixed(0)}k`} />
                    <Tooltip 
                      formatter={(value) => [formatCurrency(Number(value)), '收入']}
                      labelFormatter={(label, payload) => {
                        const item = payload?.[0]?.payload;
                        return item?.fullName || label;
                      }}
                    />
                    <Bar dataKey="revenue" fill="#1890ff" />
                  </BarChart>
                </ResponsiveContainer>
                <Divider />
              </>
            )}
            
            <Table
              dataSource={data.hotelRevenue.breakdown}
              columns={hotelColumns}
              rowKey="hotelId"
              pagination={false}
              size="small"
              scroll={{ y: 200 }}
            />
          </Card>
        </Col>

        {/* 套餐收入统计 */}
        <Col xs={24} lg={12}>
          <Card title="套餐收入统计" style={{ height: '100%' }}>
            {data.packageRevenue.breakdown.length > 0 ? (
              <Table
                dataSource={data.packageRevenue.breakdown}
                columns={packageColumns}
                rowKey="packageId"
                pagination={false}
                size="small"
                scroll={{ y: 450 }}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Text type="secondary">暂无套餐收入数据</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default RevenueStatistics;
