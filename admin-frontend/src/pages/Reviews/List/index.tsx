// 评价管理页面

import React, { useState } from 'react';
import {
  Card,
  Button,
  Space,
  Tag,
  Rate,
  Modal,
  Input,
  Select,
  Tooltip,
  Avatar,
  Typography,
  Popconfirm,
  Form,
} from 'antd';
import {
  MessageOutlined,
  EyeInvisibleOutlined,
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined,
  ExportOutlined,
  StarOutlined,
} from '@ant-design/icons';
import { adminApi } from '@/services/admin';
import { Review } from '@/types/admin';
import { notify } from '@/utils/notification';
import AdminTable from '@/components/AdminTable';
import { useTable } from '@/hooks/useTable';
import './index.css';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const ReviewList: React.FC = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [currentReview, setCurrentReview] = useState<Review | null>(null);
  const [replyForm] = Form.useForm();
  const [filters, setFilters] = useState<Record<string, any>>({});

  // 使用自定义表格Hook
  const {
    data: reviews,
    loading,
    pagination,
    selectedRowKeys: tableSelectedRowKeys,
    setSelectedRowKeys: setTableSelectedRowKeys,
    handleTableChange,
    refresh,
    search,
  } = useTable<Review>({
    apiCall: adminApi.getReviews,
    defaultPageSize: 20,
  });

  // 状态标签配置
  const getStatusTag = (status: string) => {
    const statusConfig = {
      PENDING: { color: 'orange', text: '待审核' },
      APPROVED: { color: 'green', text: '已通过' },
      REJECTED: { color: 'red', text: '已拒绝' },
      HIDDEN: { color: 'gray', text: '已隐藏' },
    };
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 评分显示
  const getRatingDisplay = (rating: number) => (
    <Space>
      <Rate disabled value={rating} style={{ fontSize: 14 }} />
      <Text strong>{rating}</Text>
    </Space>
  );

  // 审核评价
  const handleApprove = async (id: number, approved: boolean) => {
    try {
      await adminApi.approveReview(id, approved);
      notify.success(`评价${approved ? '通过' : '拒绝'}成功`);
      refresh();
    } catch (error) {
      notify.error(`评价${approved ? '通过' : '拒绝'}失败`);
    }
  };

  // 回复评价
  const handleReply = (review: Review) => {
    // 检查评价状态
    if (review.status !== 'APPROVED') {
      const statusMessages = {
        PENDING: '评价还未审核通过，请先审核后再回复',
        REJECTED: '已拒绝的评价无法回复',
        HIDDEN: '已隐藏的评价无法回复'
      };
      const message = statusMessages[review.status as keyof typeof statusMessages] || '评价状态异常，无法回复';
      notify.warning(message);
      return;
    }

    setCurrentReview(review);
    setReplyModalVisible(true);
    replyForm.setFieldsValue({
      reply: review.adminReply || '',
    });
  };

  // 提交回复
  const handleReplySubmit = async () => {
    try {
      const values = await replyForm.validateFields();
      if (currentReview) {
        await adminApi.replyToReview(currentReview.id, values.reply);
        notify.success('回复成功');
        setReplyModalVisible(false);
        setCurrentReview(null);
        replyForm.resetFields();
        refresh();
      }
    } catch (error: any) {
      console.error('❌ API Error:', error.response?.status, 'POST /admin/reviews/' + currentReview?.id + '/reply');

      // 根据错误类型显示不同的提示
      if (error.response?.status === 400) {
        const errorMessage = error.response?.data?.message || '回复失败';
        notify.error(errorMessage);
      } else if (error.response?.status === 500) {
        notify.error('🔥 Server Error: 服务器内部错误');
      } else {
        notify.error('❌ 回复失败');
      }
    }
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    search({ search: value });
  };

  // 处理筛选
  const handleFilter = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    search(newFilters);
  };

  // 重置筛选
  const handleReset = () => {
    setFilters({});
    search({});
  };

  // 隐藏评价
  const handleHide = async (id: number) => {
    try {
      await adminApi.hideReview(id);
      notify.success('评价隐藏成功');
      refresh();
    } catch (error) {
      notify.error('评价隐藏失败');
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '评价ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      sorter: true,
    },
    {
      title: '用户信息',
      key: 'user',
      width: 150,
      render: (record: Review) => (
        <Space>
          <Avatar size="small" src={record.user?.avatarUrl}>
            {record.user?.fullName?.charAt(0)}
          </Avatar>
          <div>
            <div>{record.user?.fullName}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.user?.username}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '酒店',
      key: 'hotel',
      width: 150,
      render: (record: Review) => (
        <div>
          <div>{record.hotel?.name}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.hotel?.city}
          </Text>
        </div>
      ),
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      render: getRatingDisplay,
      sorter: true,
    },
    {
      title: '评价内容',
      dataIndex: 'comment',
      key: 'comment',
      width: 300,
      render: (comment: string) => (
        <Tooltip title={comment}>
          <Paragraph
            ellipsis={{ rows: 2, expandable: false }}
            style={{ marginBottom: 0, maxWidth: 280 }}
          >
            {comment}
          </Paragraph>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: getStatusTag,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString(),
      sorter: true,
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (record: Review) => (
        <Space size="small">
          {record.status === 'PENDING' && (
            <>
              <Tooltip title="通过审核">
                <Popconfirm
                  title="确认通过这条评价吗？"
                  onConfirm={() => handleApprove(record.id, true)}
                  okText="确认"
                  cancelText="取消"
                >
                  <Button
                    type="primary"
                    size="small"
                    icon={<CheckOutlined />}
                    style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                  />
                </Popconfirm>
              </Tooltip>
              <Tooltip title="拒绝审核">
                <Popconfirm
                  title="确认拒绝这条评价吗？"
                  onConfirm={() => handleApprove(record.id, false)}
                  okText="确认"
                  cancelText="取消"
                >
                  <Button
                    type="primary"
                    size="small"
                    icon={<CloseOutlined />}
                    danger
                  />
                </Popconfirm>
              </Tooltip>
            </>
          )}
          <Tooltip title={
            record.status === 'APPROVED'
              ? (record.adminReply ? '修改回复' : '回复评价')
              : record.status === 'PENDING'
                ? '请先审核通过后再回复'
                : '此状态下无法回复'
          }>
            <Button
              type="default"
              size="small"
              icon={<MessageOutlined />}
              onClick={() => handleReply(record)}
              disabled={record.status !== 'APPROVED'}
              style={{
                opacity: record.status !== 'APPROVED' ? 0.5 : 1,
                backgroundColor: record.adminReply ? '#e6f7ff' : undefined,
                borderColor: record.adminReply ? '#1890ff' : undefined
              }}
            />
          </Tooltip>
          {record.status !== 'HIDDEN' && (
            <Tooltip title="隐藏评价">
              <Popconfirm
                title="确认隐藏这条评价吗？"
                onConfirm={() => handleHide(record.id)}
                okText="确认"
                cancelText="取消"
              >
                <Button
                  type="default"
                  size="small"
                  icon={<EyeInvisibleOutlined />}
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="review-list fade-in">
      <div className="page-header">
        <Title level={2} className="page-header-title">
          <StarOutlined style={{ marginRight: 8 }} />
          评价管理
        </Title>
      </div>

      {/* 筛选器 */}
      <Card className="mb-16">
        <Space wrap>
          <Select
            placeholder="选择状态"
            style={{ width: 120 }}
            value={filters.status || ''}
            onChange={(value) => handleFilter('status', value)}
            allowClear
          >
            <Option value="">全部</Option>
            <Option value="PENDING">待审核</Option>
            <Option value="APPROVED">已通过</Option>
            <Option value="REJECTED">已拒绝</Option>
            <Option value="HIDDEN">已隐藏</Option>
          </Select>
          <Select
            placeholder="选择评分"
            style={{ width: 120 }}
            value={filters.rating || ''}
            onChange={(value) => handleFilter('rating', value)}
            allowClear
          >
            <Option value="">全部</Option>
            <Option value="5">5星</Option>
            <Option value="4">4星</Option>
            <Option value="3">3星</Option>
            <Option value="2">2星</Option>
            <Option value="1">1星</Option>
          </Select>
          <Button onClick={handleReset}>重置</Button>
        </Space>
      </Card>

      {/* 表格 */}
      <AdminTable
        columns={columns}
        dataSource={reviews}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        rowKey="id"
        selectedRowKeys={tableSelectedRowKeys}
        onSelectChange={setTableSelectedRowKeys}
        showSearch
        searchPlaceholder="搜索评价内容、用户名、酒店名称"
        onSearch={handleSearch}
        actions={{
          refresh: {
            onClick: refresh,
          },
          export: {
            onClick: () => notify.info('导出功能开发中'),
          },
        }}
      />

      {/* 回复评价模态框 */}
      <Modal
        title="回复评价"
        open={replyModalVisible}
        onOk={handleReplySubmit}
        onCancel={() => {
          setReplyModalVisible(false);
          setCurrentReview(null);
          replyForm.resetFields();
        }}
        width={600}
        okText="提交回复"
        cancelText="取消"
      >
        {currentReview && (
          <div>
            <Card size="small" style={{ marginBottom: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>用户：</Text>
                  <Text>{currentReview.user?.fullName}</Text>
                </div>
                <div>
                  <Text strong>酒店：</Text>
                  <Text>{currentReview.hotel?.name}</Text>
                </div>
                <div>
                  <Text strong>评分：</Text>
                  {getRatingDisplay(currentReview.rating)}
                </div>
                <div>
                  <Text strong>评价内容：</Text>
                  <Paragraph style={{ marginTop: 8 }}>
                    {currentReview.comment}
                  </Paragraph>
                </div>
              </Space>
            </Card>

            <Form form={replyForm} layout="vertical">
              <Form.Item
                name="reply"
                label="管理员回复"
                rules={[
                  { required: true, message: '请输入回复内容' },
                  { min: 10, message: '回复内容至少10个字符' },
                  { max: 500, message: '回复内容不能超过500个字符' },
                ]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入您的回复..."
                  showCount
                  maxLength={500}
                />
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ReviewList;
