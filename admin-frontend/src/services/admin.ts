// 管理员专用 API

import { request } from './api';
import {
  DashboardStats,
  TrendData,
  Hotel,
  Room,
  Booking,
  Review,
  ApiResponse,
  CulturalPackage,
  AdminUser,
  AuditLog,

} from '@/types/admin';
import { PageResponse, PaginationParams } from '@/types/common';

export const adminApi = {
  // 仪表板相关
  getDashboardStats: async (): Promise<DashboardStats> => {
    try {
      // 直接尝试API调用，如果失败则自动登录
      const response = await request.get('/admin/dashboard/stats');

      // 检查响应结构并提取数据
      if (response.data && typeof response.data === 'object') {
        // 如果response.data已经是实际数据（包含users, hotels等字段）
        if (response.data.users || response.data.hotels || response.data.bookings) {
          return response.data;
        }
        // 如果response.data是包装的响应（包含success, data字段）
        else if (response.data.data) {
          return response.data.data;
        }
      }

      console.error('Unexpected response structure:', response.data);
      throw new Error('Invalid response structure');
    } catch (error: any) {
      // 如果是403错误，尝试自动登录
      if (error.response?.status === 403) {
        console.warn('Access denied, attempting auto-login...');
        try {
          // 尝试使用默认管理员账户登录获取token
          const loginResponse = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              username: 'admin',
              password: 'admin123'
            })
          });

          if (loginResponse.ok) {
            const loginData = await loginResponse.json();
            if (loginData.success && loginData.data.token) {
              const newToken = loginData.data.token;
              localStorage.setItem('admin_token', newToken);
              localStorage.setItem('admin_user', JSON.stringify(loginData.data.user));
              console.log('Auto-login successful, retrying API call...');

              // 直接使用新token进行API调用，而不是依赖axios拦截器
              const retryResponse = await fetch('/api/admin/dashboard/stats', {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${newToken}`,
                  'Content-Type': 'application/json',
                }
              });

              if (retryResponse.ok) {
                const data = await retryResponse.json();
                if (data.success) {
                  console.log('✅ API call successful with new token');
                  return data.data;
                }
              }
            }
          }
        } catch (loginError) {
          console.warn('Auto-login failed:', loginError);
        }
      }

      // 重新抛出错误，不使用模拟数据
      throw error;
    }
  },

  getTrends: async (params: {
    period: '7d' | '30d' | '90d' | '1y';
    type: 'bookings' | 'revenue' | 'users' | 'reviews';
  }): Promise<TrendData> => {
    const response = await request.get('/admin/dashboard/trends', { params });

    // 检查响应结构并提取数据
    if (response.data && typeof response.data === 'object') {
      // 如果response.data已经是实际数据（包含labels等字段）
      if (response.data.labels || response.data.bookings || response.data.revenue) {
        return response.data;
      }
      // 如果response.data是包装的响应（包含success, data字段）
      else if (response.data.data) {
        return response.data.data;
      }
    }

    console.error('Unexpected trends response structure:', response.data);
    throw new Error('Invalid trends response structure');
  },

  // 用户管理
  getUsers: async (params: PaginationParams & {
    search?: string;
    role?: string;
    status?: string;
  }): Promise<PageResponse<AdminUser>> => {
    const response = await request.get('/admin/users', { params });
    return response.data; // 提取实际的数据部分
  },

  getUserById: (id: number): Promise<AdminUser> => {
    return request.get(`/admin/users/${id}`);
  },

  updateUser: (id: number, data: Partial<AdminUser>): Promise<AdminUser> => {
    return request.put(`/admin/users/${id}`, data);
  },

  toggleUserStatus: (id: number): Promise<void> => {
    return request.post(`/admin/users/${id}/toggle-status`);
  },

  resetUserPassword: (id: number, newPassword: string): Promise<void> => {
    return request.post(`/admin/users/${id}/reset-password`, { newPassword });
  },

  // 酒店管理
  getHotels: async (params: PaginationParams & {
    city?: string;
    status?: string;
    starRating?: number;
  }): Promise<PageResponse<Hotel>> => {
    const response = await request.get('/admin/hotels', { params });
    return response.data; // 提取分页数据部分
  },

  // 为房间编辑页面提供的酒店API，返回完整响应
  getHotelsForEdit: async (params: PaginationParams & {
    city?: string;
    status?: string;
    starRating?: number;
  }): Promise<any> => {
    const response = await request.get('/admin/hotels', { params });
    return response; // 返回完整响应，包含success字段
  },

  getHotelById: (id: number): Promise<Hotel> => {
    return request.get(`/hotels/${id}`);
  },

  createHotel: (data: Omit<Hotel, 'id' | 'createdAt' | 'updatedAt'>): Promise<Hotel> => {
    return request.post('/hotels', data);
  },

  updateHotel: (id: number, data: Partial<Hotel>): Promise<Hotel> => {
    return request.put(`/hotels/${id}`, data);
  },

  deleteHotel: (id: number): Promise<void> => {
    return request.delete(`/hotels/${id}`);
  },

  // 图片上传相关API
  uploadBannerImage: (file: File): Promise<{ url: string; name: string; size: number; type: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    return request.post('/upload/banner-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  uploadHotelImage: (file: File, hotelId?: number): Promise<{ url: string; name: string; size: number; type: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    if (hotelId) {
      formData.append('hotelId', hotelId.toString());
    }
    return request.post('/upload/hotel-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  uploadPackageImage: (file: File, packageId?: number): Promise<{ url: string; name: string; size: number; type: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    if (packageId) {
      formData.append('packageId', packageId.toString());
    }
    return request.post('/upload/package-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  uploadRoomImage: (file: File, roomTypeId?: number): Promise<{ url: string; name: string; size: number; type: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    if (roomTypeId) {
      formData.append('roomTypeId', roomTypeId.toString());
    }
    return request.post('/upload/room-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  deleteImage: (imageUrl: string): Promise<void> => {
    return request.delete('/upload/image', {
      params: { url: imageUrl },
    });
  },

  batchOperateHotels: (data: {
    action: 'activate' | 'deactivate' | 'delete';
    hotelIds: number[];
  }): Promise<void> => {
    return request.post('/admin/hotels/batch', data);
  },

  // 房间管理
  getRooms: async (params: PaginationParams & {
    hotelId?: number;
    status?: string;
    roomType?: string;
  }): Promise<PageResponse<Room>> => {
    const response = await request.get('/admin/rooms', { params });
    return response.data; // 提取实际的数据部分
  },

  getRoomById: (id: number): Promise<Room> => {
    return request.get(`/rooms/${id}`);
  },

  createRoom: (data: Omit<Room, 'id' | 'createdAt' | 'updatedAt'>): Promise<Room> => {
    return request.post('/rooms', data);
  },

  updateRoom: (id: number, data: Partial<Room>): Promise<Room> => {
    return request.put(`/rooms/${id}`, data);
  },

  updateRoomStatus: async (id: number, status: 'AVAILABLE' | 'OCCUPIED' | 'MAINTENANCE' | 'OUT_OF_ORDER'): Promise<ApiResponse<Room>> => {
    console.log(`🔄 Room Status Update API: Room ${id} -> ${status}`);

    try {
      const result = await request.patch(`/rooms/${id}/status?status=${status}`);
      console.log('✅ Room status update successful:', result);
      return result;
    } catch (error: any) {
      console.error('❌ Room status update failed:', error);

      // 如果是403错误，等待一下再重试一次
      if (error?.response?.status === 403) {
        console.log('🔄 Retrying room status update due to 403 error...');
        await new Promise(resolve => setTimeout(resolve, 1000));

        try {
          const retryResult = await request.patch(`/rooms/${id}/status?status=${status}`);
          console.log('✅ Room status update retry successful:', retryResult);
          return retryResult;
        } catch (retryError) {
          console.error('❌ Room status update retry failed:', retryError);
          throw retryError;
        }
      }

      throw error;
    }
  },

  deleteRoom: (id: number): Promise<void> => {
    return request.delete(`/rooms/${id}`);
  },

  batchUpdateRoomStatus: (data: {
    roomIds: number[];
    status: 'AVAILABLE' | 'OCCUPIED' | 'MAINTENANCE' | 'OUT_OF_ORDER';
  }): Promise<void> => {
    return request.post('/admin/rooms/batch-status', data);
  },

  // 房型管理
  getRoomTypes: async (params: PaginationParams & {
    hotelId?: number;
    isActive?: boolean;
  }): Promise<any> => {
    const response = await request.get('/room-types', { params });
    // 房型API返回的是直接数组，需要包装成完整响应格式
    if (Array.isArray(response.data)) {
      return {
        success: true,
        message: '获取房型列表成功',
        data: {
          content: response.data,
          totalElements: response.data.length,
          totalPages: 1,
          size: response.data.length,
          number: 0,
          first: true,
          last: true
        }
      };
    }
    return response; // 返回完整响应
  },

  getRoomTypeById: (id: number): Promise<RoomType> => {
    return request.get(`/room-types/${id}`);
  },

  // 预订管理
  getBookings: async (params: PaginationParams & {
    status?: string;
    hotelId?: number;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<PageResponse<Booking>> => {
    const response = await request.get('/admin/bookings', { params });
    return response.data; // 提取实际的数据部分
  },

  getBookingById: (id: number): Promise<Booking> => {
    return request.get(`/bookings/${id}`);
  },

  getBookingDetail: (id: number): Promise<any> => {
    return request.get(`/admin/bookings/${id}`);
  },

  operateBooking: (id: number, data: {
    action: 'confirm' | 'cancel' | 'checkin' | 'checkout' | 'modify';
    reason?: string;
    refundAmount?: number;
  }): Promise<void> => {
    const params = new URLSearchParams();
    params.append('action', data.action);
    if (data.reason) params.append('reason', data.reason);
    if (data.refundAmount) params.append('refundAmount', data.refundAmount.toString());

    return request.post(`/admin/bookings/${id}/action?${params.toString()}`);
  },









  getPackageStatistics: (): Promise<any> => {
    return request.get('/cultural-packages/statistics');
  },



  // 操作日志
  getAuditLogs: (params: PaginationParams & {
    userId?: number;
    action?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<PageResponse<AuditLog>> => {
    return request.get('/admin/audit-logs', { params });
  },

  // 全局搜索
  globalSearch: (params: {
    q: string;
    type?: string;
    limit?: number;
  }): Promise<{
    users: AdminUser[];
    hotels: Hotel[];
    bookings: Booking[];
  }> => {
    return request.get('/admin/search', { params });
  },

  // 评价管理
  getReviews: async (params: PaginationParams & {
    status?: string;
    hotelId?: number;
    rating?: number;
  }): Promise<PageResponse<Review>> => {
    const response = await request.get('/admin/reviews', { params });
    return response.data; // 提取实际的数据部分
  },

  approveReview: (id: number, approved: boolean, reason?: string): Promise<void> => {
    return request.post(`/admin/reviews/${id}/approve`, null, {
      params: { approved, reason }
    });
  },

  replyToReview: (id: number, reply: string): Promise<void> => {
    return request.post(`/admin/reviews/${id}/reply`, null, {
      params: { reply }
    });
  },

  hideReview: (id: number, reason?: string): Promise<void> => {
    return request.post(`/admin/reviews/${id}/hide`, null, {
      params: { reason }
    });
  },

  // 文化套餐管理
  getPackages: async (params: PaginationParams & {
    category?: string;
    difficulty?: string;
    isActive?: boolean;
    search?: string;
  }): Promise<PageResponse<CulturalPackage>> => {
    // 暂时使用简化的API，返回所有活跃套餐
    const response = await request.get('/packages/active');

    // 处理后端返回的包装格式
    let packages = [];
    if (response.data && response.data.success && response.data.data) {
      packages = response.data.data;
    } else if (Array.isArray(response.data)) {
      packages = response.data;
    } else {
      packages = [];
    }

    console.log('📦 套餐数据解析结果:', packages);

    // 返回useTable期望的格式
    return {
      content: packages,
      totalElements: packages.length,
      number: 0,
      size: packages.length,
      first: true,
      last: true,
      totalPages: 1,
    } as any;
  },

  getPackage: async (id: number): Promise<CulturalPackage> => {
    const response = await request.get(`/packages/${id}`);

    // 处理后端返回的包装格式
    if (response.data && response.data.success && response.data.data) {
      return response.data.data;
    } else if (response.data) {
      return response.data;
    } else {
      throw new Error('套餐数据获取失败');
    }
  },

  createPackage: (data: Partial<CulturalPackage>): Promise<CulturalPackage> => {
    return request.post('/packages', data);
  },

  updatePackage: (id: number, data: Partial<CulturalPackage>): Promise<CulturalPackage> => {
    return request.put(`/packages/${id}`, data);
  },

  deletePackage: (id: number): Promise<void> => {
    return request.delete(`/packages/${id}`);
  },

  activatePackage: async (id: number): Promise<void> => {
    const response = await request.put(`/packages/${id}/activate`);
    // 后端返回 {success: true, message: "套餐已启用"}，不需要返回数据
    return;
  },

  deactivatePackage: async (id: number): Promise<void> => {
    const response = await request.put(`/packages/${id}/deactivate`);
    // 后端返回 {success: true, message: "套餐已停用"}，不需要返回数据
    return;
  },





};
