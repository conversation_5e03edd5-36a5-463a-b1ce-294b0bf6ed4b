// API 基础配置和拦截器

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { ApiResponse } from '@/types/common';
import { notify } from '@/utils/notification';

// Token验证函数
const isTokenValid = (token: string): boolean => {
  if (!token) return false;

  try {
    // 检查JWT格式
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    // 解析payload检查过期时间
    // 使用更安全的base64解码方式处理包含中文的JWT
    const base64Payload = parts[1];
    // 添加padding如果需要
    const paddedPayload = base64Payload + '='.repeat((4 - base64Payload.length % 4) % 4);

    let payload;
    try {
      // 尝试直接解码
      payload = JSON.parse(atob(paddedPayload));
    } catch (e) {
      // 如果失败，可能是因为中文字符，我们简化验证逻辑
      // 减少日志输出，避免控制台污染
      // console.warn('JWT payload contains non-ASCII characters, using simplified validation');
      // 对于包含中文的JWT，我们只检查格式，不验证过期时间
      return true;
    }

    const currentTime = Math.floor(Date.now() / 1000);

    // 如果token已过期，认为无效
    return payload.exp && payload.exp > currentTime;
  } catch (error) {
    console.error('Token validation error:', error);
    // 如果验证失败但token格式正确，我们认为token可能有效
    const parts = token.split('.');
    return parts.length === 3;
  }
};

// 获取有效token
const getValidToken = (): string | null => {
  const token = localStorage.getItem('admin_token');
  if (!token || !isTokenValid(token)) {
    console.warn('Token is invalid or expired');
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
    return null;
  }
  return token;
};

// 创建 axios 实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 15000, // 增加超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 减少日志输出，只在开发环境输出
    if (process.env.NODE_ENV === 'development') {
      console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    }

    // 添加认证 token
    const token = getValidToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Token added to request');
      }
    } else {
      console.warn('⚠️ No valid token available');
      // 如果没有有效token且不是登录请求，重定向到登录页
      if (!config.url?.includes('/auth/login')) {
        window.location.href = '/admin/login';
        return Promise.reject(new Error('No valid token'));
      }
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }

    // 添加请求ID用于调试
    (config as any).metadata = { requestId: Date.now().toString() };

    return config;
  },
  (error) => {
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// 重试配置
const MAX_RETRIES = 2;
const RETRY_DELAY = 1000;

// 重试函数
const retryRequest = async (originalRequest: any, retryCount: number = 0): Promise<any> => {
  if (retryCount >= MAX_RETRIES) {
    throw new Error('Max retries exceeded');
  }

  console.log(`🔄 Retrying request (attempt ${retryCount + 1}/${MAX_RETRIES})`);

  // 等待一段时间后重试
  await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * (retryCount + 1)));

  // 重新获取token
  const token = getValidToken();
  if (token) {
    originalRequest.headers.Authorization = `Bearer ${token}`;
  }

  return api(originalRequest);
};

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const requestId = (response.config as any).metadata?.requestId;
    console.log(`✅ API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url} [${requestId}]`);

    const { data } = response;

    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response;
    }

    // 检查业务状态码
    if (data && data.success === false) {
      console.error('❌ Business logic error:', data.message || '请求失败');
      return Promise.reject(new Error(data.message || '请求失败'));
    }

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as any;
    const requestId = originalRequest?.metadata?.requestId;

    console.error(`❌ API Error: ${error.response?.status} ${originalRequest?.method?.toUpperCase()} ${originalRequest?.url} [${requestId}]`, error);

    // 处理网络错误
    if (!error.response) {
      console.error('🌐 Network error: 请检查网络设置或后端服务是否启动');
      return Promise.reject(error);
    }

    const { status, data } = error.response;

    switch (status) {
      case 401:
        console.error('🔐 Unauthorized: 登录已过期');
        localStorage.removeItem('admin_token');
        localStorage.removeItem('admin_user');
        window.location.href = '/admin/login';
        break;

      case 403:
        console.error('🚫 Forbidden: 权限不足或token问题');

        // 对于403错误，尝试重试（可能是临时的token问题）
        if (!originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const retryResponse = await retryRequest(originalRequest, 0);
            console.log('✅ Retry successful');
            return retryResponse;
          } catch (retryError) {
            console.error('❌ Retry failed:', retryError);
            // 如果重试失败，检查是否需要重新登录
            const token = localStorage.getItem('admin_token');
            if (!token || !isTokenValid(token)) {
              localStorage.removeItem('admin_token');
              localStorage.removeItem('admin_user');
              window.location.href = '/admin/login';
            }
          }
        }
        break;

      case 404:
        console.error('🔍 Not Found: 请求的资源不存在');
        break;

      case 500:
        console.error('🔥 Server Error: 服务器内部错误');
        break;

      case 502:
        console.error('🔌 Bad Gateway: 后端服务未启动或连接失败');
        break;

      default:
        console.error('❓ Unknown Error:', data?.message || `请求失败 (${status})`);
    }

    return Promise.reject(error);
  }
);

// Token刷新函数
export const refreshToken = async (): Promise<boolean> => {
  try {
    console.log('🔄 Attempting to refresh token...');

    const currentToken = localStorage.getItem('admin_token');
    if (!currentToken) {
      console.warn('⚠️ No token to refresh');
      return false;
    }

    // 尝试获取用户信息来验证token
    const response = await api.get('/auth/profile');

    if (response.data && response.data.success) {
      console.log('✅ Token is still valid');
      return true;
    }

    return false;
  } catch (error) {
    console.error('❌ Token refresh failed:', error);
    return false;
  }
};

// 通用请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    api.get(url, config).then((res) => res.data),

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.post(url, data, config).then((res) => res.data),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.put(url, data, config).then((res) => res.data),

  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    api.delete(url, config).then((res) => res.data),

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.patch(url, data, config).then((res) => res.data),
};

// 文件上传
export const uploadFile = (url: string, file: File, onProgress?: (progress: number) => void) => {
  const formData = new FormData();
  formData.append('file', file);
  
  return api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
  });
};

// 文件下载
export const downloadFile = (url: string, filename?: string) => {
  return api.get(url, {
    responseType: 'blob',
  }).then((response) => {
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  });
};

export default api;
