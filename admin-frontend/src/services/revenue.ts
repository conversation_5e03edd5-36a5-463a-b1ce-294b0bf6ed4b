// 收入统计 API 服务

import { request } from './api';

// 收入统计数据类型定义
export interface HotelRevenueItem {
  hotelId: number;
  hotelName: string;
  revenue: number;
  percentage: number;
}

export interface PackageRevenueItem {
  packageId: number;
  packageName: string;
  revenue: number;
  percentage: number;
}

export interface HotelRevenueData {
  total: number;
  percentage: number;
  breakdown: HotelRevenueItem[];
}

export interface PackageRevenueData {
  total: number;
  percentage: number;
  breakdown: PackageRevenueItem[];
}

export interface RevenueBreakdownData {
  hotelRevenue: HotelRevenueData;
  packageRevenue: PackageRevenueData;
  totalRevenue: number;
}

export interface RevenueSummaryData {
  hotelRevenue: number;
  packageRevenue: number;
  totalRevenue: number;
  hotelPercentage: number;
  packagePercentage: number;
}

// 收入统计 API
export const revenueApi = {
  // 获取完整收入占比统计
  getRevenueBreakdown: async (): Promise<RevenueBreakdownData> => {
    try {
      const response = await request.get('/admin/revenue/breakdown');

      if (response && response.success) {
        return response.data;
      }

      throw new Error('获取收入统计失败');
    } catch (error: any) {
      console.error('获取收入占比统计失败:', error);
      throw error;
    }
  },

  // 获取酒店收入统计
  getHotelRevenue: async (): Promise<HotelRevenueData> => {
    try {
      const response = await request.get('/admin/revenue/hotels');

      if (response && response.success) {
        return response.data;
      }

      throw new Error('获取酒店收入统计失败');
    } catch (error: any) {
      console.error('获取酒店收入统计失败:', error);
      throw error;
    }
  },

  // 获取套餐收入统计
  getPackageRevenue: async (): Promise<PackageRevenueData> => {
    try {
      const response = await request.get('/admin/revenue/packages');

      if (response && response.success) {
        return response.data;
      }

      throw new Error('获取套餐收入统计失败');
    } catch (error: any) {
      console.error('获取套餐收入统计失败:', error);
      throw error;
    }
  },

  // 获取收入摘要
  getRevenueSummary: async (): Promise<RevenueSummaryData> => {
    try {
      const response = await request.get('/admin/revenue/summary');

      if (response && response.success) {
        return response.data;
      }

      throw new Error('获取收入摘要失败');
    } catch (error: any) {
      console.error('获取收入摘要失败:', error);
      throw error;
    }
  },
};
