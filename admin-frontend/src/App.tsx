// 主应用组件

import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

// 设置 dayjs 中文
dayjs.locale('zh-cn');

// 导入组件
import AdminLayout from '@/layouts/AdminLayout';
import ProtectedRoute from '@/components/ProtectedRoute';
import ErrorBoundary from '@/components/ErrorBoundary';
import { PageLoading } from '@/components/Loading';
import Login from '@/pages/Login';
import { AuthProvider } from '@/hooks/useAuth';

// 导入样式
import '@/styles/responsive.css';

// 导入测试工具（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  import('@/utils/runTests');
}

// 导入自动登录工具
import { initAutoLogin } from '@/utils/autoLogin';

// 懒加载页面组件
const Dashboard = React.lazy(() => import('@/pages/Dashboard'));
const HotelList = React.lazy(() => import('@/pages/Hotels/List'));
const HotelEdit = React.lazy(() => import('@/pages/Hotels/Edit'));
const RoomList = React.lazy(() => import('@/pages/Rooms/List'));
const RoomCreate = React.lazy(() => import('@/pages/Rooms/Create'));
const RoomDetail = React.lazy(() => import('@/pages/Rooms/Detail'));
const RoomEdit = React.lazy(() => import('@/pages/Rooms/Edit'));
const RoomSchedule = React.lazy(() => import('@/pages/Rooms/Schedule'));
const BookingList = React.lazy(() => import('@/pages/Bookings/List'));
const BookingDetail = React.lazy(() => import('@/pages/Bookings/Detail'));
const UserList = React.lazy(() => import('@/pages/Users/<USER>'));
const UserDetail = React.lazy(() => import('@/pages/Users/<USER>'));
const UserEdit = React.lazy(() => import('@/pages/Users/<USER>'));
const ReviewList = React.lazy(() => import('@/pages/Reviews/List'));
const ReviewPending = React.lazy(() => import('@/pages/Reviews/Pending'));
const PackageList = React.lazy(() => import('@/pages/Packages/List'));
const PackageCreate = React.lazy(() => import('@/pages/Packages/Create'));
const PackageEdit = React.lazy(() => import('@/pages/Packages/Edit'));
const PackageBookingList = React.lazy(() => import('@/pages/PackageBookings/List'));
const PackageBookingDetail = React.lazy(() => import('@/pages/PackageBookings/Detail'));
const PackageDetail = React.lazy(() => import('@/pages/Packages/Detail'));
const PackageTest = React.lazy(() => import('@/pages/Test/PackageTest'));
const BannerList = React.lazy(() => import('@/pages/Banners/List'));
const RevenueStatistics = React.lazy(() => import('@/pages/Reports/Revenue'));

const Settings = React.lazy(() => import('@/pages/Settings'));
const Profile = React.lazy(() => import('@/pages/Profile'));
const ImageUploadDemo = React.lazy(() => import('@/pages/Demo/ImageUpload'));

// 加载中组件
const LoadingFallback: React.FC = () => <PageLoading tip="页面加载中..." />;

const App: React.FC = () => {
  // 在应用启动时执行自动登录
  React.useEffect(() => {
    initAutoLogin();
  }, []);

  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: '#1890ff',
            borderRadius: 6,
            colorBgContainer: '#ffffff',
            colorBgElevated: '#ffffff',
          },
          components: {
            Layout: {
              siderBg: '#ffffff',
              triggerBg: '#f5f5f5',
              bodyBg: '#f0f2f5',
            },
            Menu: {
              itemBg: 'transparent',
              itemSelectedBg: '#e6f7ff',
              itemHoverBg: '#f5f5f5',
              itemSelectedColor: '#1890ff',
              itemColor: '#595959',
            },
          },
        }}
      >
        <AntdApp>
          <AuthProvider>
            <Router
              future={{
                v7_startTransition: true,
                v7_relativeSplatPath: true,
              }}
            >
          <React.Suspense fallback={<LoadingFallback />}>
            <Routes>
              {/* 登录页面 */}
              <Route path="/admin/login" element={<Login />} />
              
              {/* 管理员后台路由 */}
              <Route
                path="/admin"
                element={
                  <ProtectedRoute>
                    <AdminLayout />
                  </ProtectedRoute>
                }
              >
                {/* 仪表板 */}
                <Route path="dashboard" element={<Dashboard />} />
                
                {/* 酒店管理 */}
                <Route path="hotels/list" element={<HotelList />} />
                <Route path="hotels/edit/:id" element={<HotelEdit />} />
                
                {/* 房间管理 */}
                <Route path="rooms" element={<RoomList />} />
                <Route path="rooms/create" element={<RoomCreate />} />
                <Route path="rooms/view/:id" element={<RoomDetail />} />
                <Route path="rooms/edit/:id" element={<RoomEdit />} />
                <Route path="rooms/schedule" element={<RoomSchedule />} />
                
                {/* 预订管理 */}
                <Route path="bookings" element={<BookingList />} />
                <Route path="bookings/view/:id" element={<BookingDetail />} />
                
                {/* 用户管理 */}
                <Route path="users" element={<UserList />} />
                <Route path="users/view/:id" element={<UserDetail />} />
                <Route path="users/edit/:id" element={<UserEdit />} />
                
                {/* 评价管理 */}
                <Route path="reviews/list" element={<ReviewList />} />
                <Route path="reviews/pending" element={<ReviewPending />} />
                
                {/* 文化套餐管理 */}
                <Route path="packages" element={<PackageList />} />
                <Route path="packages/create" element={<PackageCreate />} />
                <Route path="packages/edit/:id" element={<PackageEdit />} />
                <Route path="packages/detail/:id" element={<PackageDetail />} />

                {/* 文化套餐预订管理 */}
                <Route path="package-bookings" element={<PackageBookingList />} />
                <Route path="package-bookings/detail/:id" element={<PackageBookingDetail />} />

                {/* 轮播图管理 */}
                <Route path="banners" element={<BannerList />} />

                {/* 收入统计 */}
                <Route path="reports/revenue" element={<RevenueStatistics />} />

                {/* 测试页面 */}
                <Route path="test/packages" element={<PackageTest />} />





                {/* 系统设置 */}
                <Route path="settings" element={<Settings />} />

                {/* 个人设置 */}
                <Route path="profile" element={<Profile />} />

                {/* 演示页面 */}
                <Route path="demo/image-upload" element={<ImageUploadDemo />} />
                
                {/* 默认重定向到仪表板 */}
                <Route index element={<Navigate to="dashboard" replace />} />
              </Route>
              
              {/* 根路径重定向 */}
              <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
              
              {/* 404 页面 */}
              <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
            </Routes>
          </React.Suspense>
            </Router>
          </AuthProvider>
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App;
