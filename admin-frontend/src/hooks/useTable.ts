// 表格数据管理 Hook

import { useState, useCallback, useEffect, useRef } from 'react';
import { PageResponse, PaginationParams } from '@/types/common';
import { notify } from '@/utils/notification';

interface UseTableOptions<T> {
  apiCall: (params: any) => Promise<PageResponse<T>>;
  defaultPageSize?: number;
  defaultFilters?: Record<string, any>;
  dependencies?: any[];
}

interface UseTableReturn<T> {
  data: T[];
  loading: boolean;
  pagination: {
    current: number;
    pageSize: number;
    total: number;
    showSizeChanger: boolean;
    showQuickJumper: boolean;
    showTotal: (total: number, range: [number, number]) => string;
  };
  filters: Record<string, any>;
  selectedRowKeys: React.Key[];
  setFilters: (filters: Record<string, any>) => void;
  setSelectedRowKeys: (keys: React.Key[]) => void;
  handleTableChange: (pagination: any, filters: any, sorter: any) => void;
  refresh: () => void;
  search: (searchFilters?: Record<string, any>) => void;
}

export const useTable = <T extends { id: number | string }>({
  apiCall,
  defaultPageSize = 20,
  defaultFilters = {},
  dependencies = [],
}: UseTableOptions<T>): UseTableReturn<T> => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: defaultPageSize,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  });
  const [filters, setFilters] = useState<Record<string, any>>(defaultFilters);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // 使用ref存储最新的pagination和filters值，避免在loadData中形成依赖循环
  const paginationRef = useRef(pagination);
  const filtersRef = useRef(filters);

  // 更新ref值
  useEffect(() => {
    paginationRef.current = pagination;
  }, [pagination]);

  useEffect(() => {
    filtersRef.current = filters;
  }, [filters]);

  // 加载数据
  const loadData = useCallback(async (params: Record<string, any> = {}) => {
    try {
      setLoading(true);

      const currentPagination = paginationRef.current;
      const currentFilters = filtersRef.current;

      const requestParams = {
        page: Math.max(0, (currentPagination.current || 1) - 1), // 后端从0开始，确保不为负数或NaN
        size: currentPagination.pageSize || defaultPageSize,
        ...currentFilters,
        ...params,
      };

      const response = await apiCall(requestParams);

      setData(response.content || []);
      setPagination(prev => ({
        ...prev,
        total: response.totalElements || 0,
        current: (response.number ?? 0) + 1, // 前端从1开始，确保不为NaN
      }));

      // 清空选中项
      setSelectedRowKeys([]);
    } catch (error: any) {
      console.error('Load data failed:', error);
      const errorMessage = error.response?.data?.message || error.message || '加载数据失败';
      notify.error('加载数据失败', errorMessage);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [apiCall, defaultPageSize]); // 只依赖apiCall和defaultPageSize，避免无限循环

  // 处理表格变化
  const handleTableChange = useCallback((newPagination: any, newFilters: any, sorter: any) => {
    // 更新分页
    setPagination(prev => ({
      ...prev,
      current: newPagination.current,
      pageSize: newPagination.pageSize,
    }));

    // 处理筛选器
    const processedFilters: Record<string, any> = {};
    Object.keys(newFilters).forEach(key => {
      const value = newFilters[key];
      if (value && value.length > 0) {
        processedFilters[key] = value;
      }
    });

    // 处理排序
    if (sorter && sorter.field) {
      processedFilters.sortBy = sorter.field;
      processedFilters.sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
    }

    setFilters(prev => ({ ...prev, ...processedFilters }));
  }, []);

  // 刷新数据
  const refresh = useCallback(() => {
    loadData();
  }, [loadData]);

  // 搜索
  const search = useCallback((searchFilters: Record<string, any> = {}) => {
    // 重置到第一页
    setPagination(prev => ({ ...prev, current: 1 }));
    
    // 更新筛选条件
    setFilters(prev => ({ ...prev, ...searchFilters }));
  }, []);

  // 初始化时加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 监听外部依赖变化，重新加载数据
  useEffect(() => {
    if (dependencies.length > 0) {
      loadData();
    }
  }, dependencies);

  // 监听pagination.current和pagination.pageSize变化，重新加载数据
  useEffect(() => {
    loadData();
  }, [pagination.current, pagination.pageSize]);

  // 监听filters变化，重置到第一页并重新加载数据
  useEffect(() => {
    setPagination(prev => ({ ...prev, current: 1 }));
    loadData();
  }, [filters]);

  return {
    data,
    loading,
    pagination,
    filters,
    selectedRowKeys,
    setFilters,
    setSelectedRowKeys,
    handleTableChange,
    refresh,
    search,
  };
};
