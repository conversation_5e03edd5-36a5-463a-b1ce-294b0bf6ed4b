# 甘孜州酒店预订系统 - 管理员后台开发文档

## 📋 项目概述

本文档详细描述了甘孜州酒店预订系统管理员后台的开发规划、技术架构和实现方案。

### 当前状态

- **后端管理 API**: ✅ 已完成
- **前端管理界面**: ⏳ 待开发
- **权限控制**: ✅ 已完成
- **数据初始化**: ✅ 已完成

## 🏗️ 系统架构

### 技术栈

- **前端**: React 18 + Ant Design Pro + TypeScript
- **后端**: Spring Boot 3.2.1 + Spring Security + JWT
- **数据库**: MySQL 5.7+
- **权限管理**: 基于角色的访问控制 (RBAC)

### 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   管理员前端     │    │    后端API      │    │     数据库      │
│  (React Admin)  │◄──►│  (Spring Boot)  │◄──►│    (MySQL)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔐 权限系统

### 用户角色

- **ADMIN**: 系统管理员，拥有所有权限
- **USER**: 普通用户，只能访问前台功能

### 默认管理员账户

```
用户名: admin
密码: admin123
邮箱: <EMAIL>
角色: ADMIN
```

### API 权限控制

```java
// 管理员专用端点
@RequestMapping("/admin/**").hasRole("ADMIN")

// 部分管理功能（临时开放用于测试）
@PreAuthorize("hasRole('ADMIN')") // 部分被注释
```

## 📊 功能模块设计

### 1. 仪表板 (Dashboard)

**路由**: `/admin/dashboard`

**功能特性**:

- 系统概览统计
- 实时数据监控
- 快速操作入口
- 最近活动日志

**数据指标**:

- 总用户数
- 总酒店数
- 总预订数
- 总收入
- 今日新增用户
- 今日新增预订
- 系统健康状态

### 2. 酒店管理 (Hotel Management)

**路由**: `/admin/hotels`

**功能特性**:

- 酒店列表查看
- 酒店信息编辑
- 酒店状态管理
- 酒店图片管理
- 批量操作

**API 端点**:

```
GET    /api/hotels              # 获取酒店列表
POST   /api/hotels              # 创建酒店
PUT    /api/hotels/{id}         # 更新酒店
DELETE /api/hotels/{id}         # 删除酒店
```

### 3. 房间管理 (Room Management)

**路由**: `/admin/rooms`

**功能特性**:

- 房间列表管理
- 房型配置
- 房间状态控制
- 价格管理
- 可用性设置

**API 端点**:

```
GET    /api/rooms               # 获取房间列表
POST   /api/rooms               # 创建房间
PUT    /api/rooms/{id}          # 更新房间
GET    /api/rooms/hotel/{id}    # 获取酒店房间
```

### 4. 预订管理 (Booking Management)

**路由**: `/admin/bookings`

**功能特性**:

- 预订列表查看
- 预订状态管理
- 预订详情查看
- 退款处理
- 预订统计报表

**API 端点**:

```
GET    /api/bookings            # 获取预订列表
GET    /api/bookings/{id}       # 获取预订详情
PUT    /api/bookings/{id}       # 更新预订状态
GET    /api/bookings/hotel/{id} # 获取酒店预订
```

### 5. 用户管理 (User Management)

**路由**: `/admin/users`

**功能特性**:

- 用户列表查看
- 用户信息编辑
- 用户状态管理
- 角色权限分配
- 用户行为分析

**需要新增的 API**:

```
GET    /api/admin/users         # 获取用户列表
PUT    /api/admin/users/{id}    # 更新用户信息
POST   /api/admin/users/{id}/disable # 禁用用户
POST   /api/admin/users/{id}/enable  # 启用用户
```

### 6. 评价管理 (Review Management)

**路由**: `/admin/reviews`

**功能特性**:

- 评价列表查看
- 评价审核
- 管理员回复
- 评价隐藏/显示
- 评价统计

**API 端点**:

```
GET    /api/reviews/pending     # 获取待审核评价
PUT    /api/reviews/{id}/approve # 审核评价
PUT    /api/reviews/{id}/reply   # 回复评价
PUT    /api/reviews/{id}/hide    # 隐藏评价
```

### 7. 文化套餐管理 (Cultural Package Management)

**路由**: `/admin/packages`

**功能特性**:

- 套餐列表管理
- 套餐内容编辑
- 套餐图片管理
- 套餐状态控制
- 预订统计

**API 端点**:

```
GET    /api/cultural-packages           # 获取套餐列表
POST   /api/cultural-packages           # 创建套餐
PUT    /api/cultural-packages/{id}      # 更新套餐
GET    /api/cultural-packages/statistics # 获取统计
```

## 🎨 UI/UX 设计规范

### 设计原则

- **一致性**: 统一的视觉风格和交互模式
- **易用性**: 简洁直观的操作界面
- **响应式**: 支持桌面端和平板端
- **可访问性**: 符合无障碍设计标准

### 色彩方案

```css
/* 主色调 */
--primary-color: #1890ff; /* 蓝色 - 主要操作 */
--success-color: #52c41a; /* 绿色 - 成功状态 */
--warning-color: #faad14; /* 橙色 - 警告状态 */
--error-color: #f5222d; /* 红色 - 错误状态 */

/* 中性色 */
--text-primary: #262626; /* 主要文本 */
--text-secondary: #8c8c8c; /* 次要文本 */
--border-color: #d9d9d9; /* 边框颜色 */
--background-color: #f0f2f5; /* 背景颜色 */
```

### 组件规范

- **表格**: 使用 Ant Design Table 组件
- **表单**: 使用 Ant Design Form 组件
- **图表**: 使用 Ant Design Charts
- **布局**: 使用 Ant Design Pro Layout

## 📱 页面结构设计

### 整体布局

```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏                            │
├─────────────┬───────────────────────────────────────────┤
│             │                                           │
│   侧边导航   │              主内容区域                    │
│             │                                           │
│   - 仪表板   │   ┌─────────────────────────────────────┐ │
│   - 酒店管理 │   │                                     │ │
│   - 房间管理 │   │          页面内容                    │ │
│   - 预订管理 │   │                                     │ │
│   - 用户管理 │   │                                     │ │
│   - 评价管理 │   └─────────────────────────────────────┘ │
│   - 套餐管理 │                                           │
│   - 系统设置 │                                           │
│             │                                           │
└─────────────┴───────────────────────────────────────────┘
```

### 路由结构

```
/admin
├── /dashboard          # 仪表板
├── /hotels            # 酒店管理
│   ├── /list          # 酒店列表
│   ├── /create        # 创建酒店
│   └── /edit/:id      # 编辑酒店
├── /rooms             # 房间管理
│   ├── /list          # 房间列表
│   ├── /create        # 创建房间
│   └── /edit/:id      # 编辑房间
├── /bookings          # 预订管理
│   ├── /list          # 预订列表
│   └── /detail/:id    # 预订详情
├── /users             # 用户管理
│   ├── /list          # 用户列表
│   └── /detail/:id    # 用户详情
├── /reviews           # 评价管理
│   ├── /list          # 评价列表
│   └── /pending       # 待审核评价
├── /packages          # 文化套餐管理
│   ├── /list          # 套餐列表
│   ├── /create        # 创建套餐
│   └── /edit/:id      # 编辑套餐
└── /settings          # 系统设置
```

## 🔧 开发计划

### 阶段一：基础框架搭建 (1-2 天)

- [ ] 创建管理员前端项目结构
- [ ] 配置路由和权限控制
- [ ] 实现管理员登录页面
- [ ] 搭建基础布局组件

### 阶段二：核心功能开发 (3-5 天)

- [ ] 开发仪表板页面
- [ ] 实现酒店管理功能
- [ ] 实现房间管理功能
- [ ] 实现预订管理功能

### 阶段三：扩展功能开发 (2-3 天)

- [ ] 实现用户管理功能
- [ ] 实现评价管理功能
- [ ] 实现文化套餐管理功能

### 阶段四：系统完善 (1-2 天)

- [ ] 实现系统设置功能
- [ ] 添加数据统计图表
- [ ] 完善错误处理和用户体验
- [ ] 进行测试和优化

## 📋 技术要求

### 前端技术栈

```json
{
  "react": "^18.0.0",
  "@ant-design/pro-layout": "^7.0.0",
  "@ant-design/pro-table": "^3.0.0",
  "@ant-design/charts": "^2.0.0",
  "typescript": "^5.0.0",
  "axios": "^1.0.0",
  "react-router-dom": "^6.0.0"
}
```

### 开发环境要求

- Node.js 18+
- npm 或 yarn
- VS Code (推荐)
- Chrome DevTools

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 使用 Prettier 进行代码格式化
- 组件采用函数式组件 + Hooks

## 🚀 部署方案

### 开发环境

```bash
# 启动后端服务
cd backend
mvn spring-boot:run

# 启动管理员前端
cd admin-frontend
npm install
npm run dev
```

### 生产环境

- 前端构建后部署到 Nginx
- 后端打包为 JAR 部署到服务器
- 使用反向代理处理跨域问题

## 📚 相关文档

- [API 接口文档](./API_SPECIFICATIONS.md)
- [数据库设计文档](./DATABASE_SCHEMA.sql)
- [前端开发规范](./FRONTEND_SPECIFICATIONS.md)
- [项目部署指南](./DEPLOYMENT_GUIDE.md)

## 🛠️ 实现细节

### 管理员认证流程

```typescript
// 管理员登录流程
interface AdminLoginFlow {
  1. 用户输入用户名和密码
  2. 前端发送登录请求到 /api/auth/login
  3. 后端验证用户身份和角色权限
  4. 返回 JWT Token 和用户信息
  5. 前端存储 Token 并跳转到管理后台
  6. 后续请求携带 Token 进行权限验证
}
```

### 状态管理方案

```typescript
// 使用 React Context + useReducer 管理全局状态
interface AdminState {
  user: AdminUser | null;
  permissions: string[];
  loading: boolean;
  error: string | null;
}

// 主要状态包括：
-管理员用户信息 - 权限列表 - 加载状态 - 错误信息 - 菜单折叠状态 - 主题设置;
```

### 数据流设计

```
用户操作 → 组件事件 → API 调用 → 后端处理 → 数据库操作 → 返回结果 → 更新界面
```

## 📊 数据统计需求

### 仪表板统计指标

```sql
-- 用户统计
SELECT
  COUNT(*) as total_users,
  COUNT(CASE WHEN created_at >= CURDATE() THEN 1 END) as today_new_users,
  COUNT(CASE WHEN role = 'ADMIN' THEN 1 END) as admin_count
FROM users WHERE is_active = true;

-- 酒店统计
SELECT
  COUNT(*) as total_hotels,
  COUNT(CASE WHEN is_active = true THEN 1 END) as active_hotels,
  AVG(star_rating) as avg_rating
FROM hotels;

-- 预订统计
SELECT
  COUNT(*) as total_bookings,
  COUNT(CASE WHEN created_at >= CURDATE() THEN 1 END) as today_bookings,
  SUM(total_amount) as total_revenue,
  SUM(CASE WHEN created_at >= CURDATE() THEN total_amount ELSE 0 END) as today_revenue
FROM bookings WHERE status != 'CANCELLED';
```

### 图表组件需求

- **折线图**: 预订趋势、收入趋势
- **柱状图**: 月度统计、酒店对比
- **饼图**: 预订状态分布、用户角色分布
- **仪表盘**: 入住率、满意度评分

## 🔒 安全考虑

### 前端安全

- JWT Token 存储在 httpOnly Cookie 中
- 实现 Token 自动刷新机制
- 路由权限守卫
- XSS 防护
- CSRF 防护

### 后端安全

- 管理员操作日志记录
- 敏感操作二次确认
- API 访问频率限制
- 数据脱敏处理
- 权限细粒度控制

### 数据安全

- 敏感数据加密存储
- 数据库连接加密
- 定期数据备份
- 操作审计日志

## 🧪 测试策略

### 单元测试

- 组件渲染测试
- 工具函数测试
- API 调用测试
- 状态管理测试

### 集成测试

- 页面交互测试
- API 集成测试
- 权限控制测试
- 数据流测试

### E2E 测试

- 管理员登录流程
- 核心业务流程
- 错误处理流程
- 浏览器兼容性测试

## 📈 性能优化

### 前端优化

- 代码分割和懒加载
- 组件缓存和 memo
- 虚拟滚动处理大数据
- 图片懒加载和压缩
- Bundle 分析和优化

### 后端优化

- 数据库查询优化
- 缓存策略实施
- 分页查询优化
- API 响应压缩
- 数据库连接池优化

## 🔄 维护和更新

### 版本管理

- 使用语义化版本控制
- 维护详细的更新日志
- 数据库迁移脚本管理
- 配置文件版本控制

### 监控和日志

- 系统性能监控
- 错误日志收集
- 用户行为分析
- API 调用统计

### 备份策略

- 数据库定期备份
- 代码版本备份
- 配置文件备份
- 恢复流程测试

## 📋 开发检查清单

### 开发前准备

- [ ] 确认技术栈和依赖版本
- [ ] 搭建开发环境
- [ ] 配置代码规范工具
- [ ] 准备设计稿和原型

### 开发过程

- [ ] 遵循代码规范
- [ ] 编写单元测试
- [ ] 进行代码审查
- [ ] 更新文档

### 测试阶段

- [ ] 功能测试
- [ ] 性能测试
- [ ] 安全测试
- [ ] 兼容性测试

### 部署上线

- [ ] 生产环境配置
- [ ] 数据迁移
- [ ] 性能监控
- [ ] 用户培训

---

**文档版本**: v1.0
**创建日期**: 2025-07-20
**最后更新**: 2025-07-20
**维护人员**: 开发团队
