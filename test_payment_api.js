const axios = require('axios');

// 测试套餐付款API
async function testPackagePaymentAPI() {
  const baseURL = 'http://localhost:8080/api';
  
  console.log('🚀 开始测试套餐付款API...\n');

  try {
    // 1. 首先获取套餐列表
    console.log('1. 获取套餐列表...');
    const packagesResponse = await axios.get(`${baseURL}/packages`);
    console.log(`✅ 获取到 ${packagesResponse.data.data.content.length} 个套餐`);
    
    if (packagesResponse.data.data.content.length === 0) {
      console.log('❌ 没有可用的套餐，无法继续测试');
      return;
    }

    const firstPackage = packagesResponse.data.data.content[0];
    console.log(`📦 使用套餐: ${firstPackage.name} (ID: ${firstPackage.id})`);

    // 2. 创建预订
    console.log('\n2. 创建套餐预订...');
    const bookingData = {
      bookingDate: '2024-12-15',
      participantCount: 2,
      contactName: '测试用户',
      contactPhone: '13800138000',
      contactEmail: '<EMAIL>',
      specialRequirements: '测试预订'
    };

    const bookingResponse = await axios.post(`${baseURL}/packages/${firstPackage.id}/book`, bookingData);
    console.log('✅ 预订创建成功');
    console.log(`📋 预订号: ${bookingResponse.data.data.bookingNumber}`);
    console.log(`💰 总金额: ¥${bookingResponse.data.data.totalAmount}`);

    const booking = bookingResponse.data.data;

    // 3. 创建付款
    console.log('\n3. 创建付款...');
    const paymentData = {
      amount: booking.totalAmount,
      currency: 'CNY',
      paymentMethod: 'ALIPAY',
      returnUrl: 'http://localhost:3003/payment/return',
      notifyUrl: 'http://localhost:8080/api/packages/payments/callback',
      remarks: '测试付款'
    };

    const paymentResponse = await axios.post(`${baseURL}/packages/${booking.id}/payments`, paymentData);
    console.log('✅ 付款创建成功');
    console.log(`💳 付款号: ${paymentResponse.data.data.paymentNumber}`);
    console.log(`🔗 付款链接: ${paymentResponse.data.data.paymentUrl || '无'}`);
    console.log(`⏰ 过期时间: ${paymentResponse.data.data.expiresAt}`);

    const payment = paymentResponse.data.data;

    // 4. 查询付款状态
    console.log('\n4. 查询付款状态...');
    const statusResponse = await axios.get(`${baseURL}/packages/payments/${payment.paymentNumber}`);
    console.log('✅ 付款状态查询成功');
    console.log(`📊 付款状态: ${statusResponse.data.data.status}`);
    console.log(`💰 付款金额: ¥${statusResponse.data.data.amount}`);

    // 5. 模拟付款成功（仅用于测试）
    console.log('\n5. 模拟付款处理...');
    const processResponse = await axios.post(`${baseURL}/packages/payments/${payment.paymentNumber}/process`);
    console.log('✅ 付款处理成功');
    console.log(`📊 最终状态: ${processResponse.data.data.status}`);

    // 6. 再次查询付款状态确认
    console.log('\n6. 确认最终付款状态...');
    const finalStatusResponse = await axios.get(`${baseURL}/packages/payments/${payment.paymentNumber}`);
    console.log('✅ 最终状态确认');
    console.log(`📊 付款状态: ${finalStatusResponse.data.data.status}`);
    console.log(`⏰ 处理时间: ${finalStatusResponse.data.data.processedAt || '未处理'}`);

    console.log('\n🎉 套餐付款API测试完成！所有功能正常工作。');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    if (error.response) {
      console.error('📄 响应状态:', error.response.status);
      console.error('📄 响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
testPackagePaymentAPI();
