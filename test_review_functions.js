#!/usr/bin/env node

/**
 * 评价功能测试脚本
 * 测试评价的创建、审核、回复等功能
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8080/api';

// 测试配置
const testConfig = {
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// 创建axios实例
const api = axios.create(testConfig);

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// 测试函数
async function runTest(testName, testFn) {
  testResults.total++;
  console.log(`\n🧪 测试: ${testName}`);
  
  try {
    await testFn();
    testResults.passed++;
    console.log(`✅ ${testName} - 通过`);
  } catch (error) {
    testResults.failed++;
    testResults.errors.push({ test: testName, error: error.message });
    console.log(`❌ ${testName} - 失败: ${error.message}`);
  }
}

// 1. 测试创建评价
async function testCreateReview() {
  const reviewData = {
    hotelId: 15, // 使用不同的酒店ID避免重复评价限制
    rating: 4,
    serviceRating: 4,
    cleanlinessRating: 4,
    locationRating: 5,
    valueRating: 3,
    comment: '测试评价功能 - 自动化测试创建'
  };

  const response = await api.post('/reviews', reviewData);
  
  if (response.data.success && response.data.data.status === 'PENDING') {
    console.log(`   📝 创建评价成功，ID: ${response.data.data.id}`);
    return response.data.data.id;
  } else {
    throw new Error('创建评价失败');
  }
}

// 2. 测试获取所有评价
async function testGetAllReviews() {
  const response = await api.get('/admin/reviews?page=0&size=20');
  
  if (response.data.success && Array.isArray(response.data.data.content)) {
    console.log(`   📋 获取到 ${response.data.data.totalElements} 条评价`);
    return response.data.data.content;
  } else {
    throw new Error('获取评价列表失败');
  }
}

// 3. 测试按状态筛选评价
async function testFilterReviewsByStatus() {
  const response = await api.get('/admin/reviews?page=0&size=10&status=PENDING');
  
  if (response.data.success) {
    const pendingReviews = response.data.data.content;
    console.log(`   🔍 找到 ${pendingReviews.length} 条待审核评价`);
    
    // 验证所有返回的评价都是PENDING状态
    const allPending = pendingReviews.every(review => review.status === 'PENDING');
    if (!allPending) {
      throw new Error('筛选结果包含非PENDING状态的评价');
    }
    
    return pendingReviews;
  } else {
    throw new Error('按状态筛选评价失败');
  }
}

// 4. 测试审核评价
async function testApproveReview(reviewId) {
  const response = await api.post(`/admin/reviews/${reviewId}/approve?approved=true`);
  
  if (response.data.success && response.data.data.status === 'APPROVED') {
    console.log(`   ✅ 审核评价成功，状态: ${response.data.data.status}`);
    return response.data.data;
  } else {
    throw new Error('审核评价失败');
  }
}

// 5. 测试回复评价
async function testReplyToReview(reviewId) {
  const replyText = '感谢您的评价，我们会继续努力提供更好的服务！';
  
  // 使用form-urlencoded格式
  const response = await axios.post(
    `${BASE_URL}/admin/reviews/${reviewId}/reply`,
    `reply=${encodeURIComponent(replyText)}`,
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }
  );
  
  if (response.data.success && response.data.data.adminReply) {
    console.log(`   💬 回复评价成功: ${response.data.data.adminReply}`);
    return response.data.data;
  } else {
    throw new Error('回复评价失败');
  }
}

// 6. 测试拒绝评价
async function testRejectReview() {
  // 先创建一个新评价用于拒绝测试
  const reviewData = {
    hotelId: 16, // 使用不同的酒店ID避免重复评价限制
    rating: 2,
    serviceRating: 2,
    cleanlinessRating: 2,
    locationRating: 2,
    valueRating: 2,
    comment: '测试拒绝功能 - 这是一个用于测试拒绝的评价'
  };

  const createResponse = await api.post('/reviews', reviewData);
  const reviewId = createResponse.data.data.id;
  
  const response = await api.post(`/admin/reviews/${reviewId}/approve?approved=false`);
  
  if (response.data.success && response.data.data.status === 'REJECTED') {
    console.log(`   ❌ 拒绝评价成功，状态: ${response.data.data.status}`);
    return response.data.data;
  } else {
    throw new Error('拒绝评价失败');
  }
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始评价功能测试...\n');
  
  let createdReviewId = null;
  
  // 运行所有测试
  await runTest('创建评价', async () => {
    createdReviewId = await testCreateReview();
  });
  
  await runTest('获取所有评价', testGetAllReviews);
  
  await runTest('按状态筛选评价', testFilterReviewsByStatus);
  
  if (createdReviewId) {
    await runTest('审核评价', async () => {
      await testApproveReview(createdReviewId);
    });
    
    await runTest('回复评价', async () => {
      await testReplyToReview(createdReviewId);
    });
  }
  
  await runTest('拒绝评价', testRejectReview);
  
  // 输出测试结果
  console.log('\n📊 测试结果统计:');
  console.log(`   总测试数: ${testResults.total}`);
  console.log(`   通过: ${testResults.passed}`);
  console.log(`   失败: ${testResults.failed}`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.errors.forEach(error => {
      console.log(`   - ${error.test}: ${error.error}`);
    });
  }
  
  if (testResults.failed === 0) {
    console.log('\n🎉 所有测试通过！评价功能正常工作。');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查相关功能。');
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('测试运行出错:', error);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  testResults
};
