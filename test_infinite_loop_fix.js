#!/usr/bin/env node

/**
 * 测试无限循环修复脚本
 * 监控API请求频率，检测是否存在无限循环
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3005/api';

// 请求计数器
let requestCount = 0;
let startTime = Date.now();

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 监控函数
async function monitorApiRequests() {
  console.log('🔍 开始监控API请求频率...');
  console.log('📊 测试时间: 30秒');
  console.log('⚠️ 如果30秒内请求超过10次，可能存在无限循环问题\n');

  const testDuration = 30000; // 30秒
  const maxAllowedRequests = 10; // 最大允许请求数
  
  // 定期发送测试请求
  const interval = setInterval(async () => {
    try {
      requestCount++;
      const response = await api.get('/admin/reviews?page=0&size=5');
      
      const elapsed = Date.now() - startTime;
      console.log(`📡 请求 #${requestCount} - 耗时: ${elapsed}ms - 状态: ${response.status}`);
      
      if (requestCount > maxAllowedRequests) {
        console.log('\n❌ 检测到可能的无限循环！');
        console.log(`   在 ${elapsed}ms 内发送了 ${requestCount} 次请求`);
        console.log('   这表明可能存在无限循环问题');
        clearInterval(interval);
        process.exit(1);
      }
      
    } catch (error) {
      console.log(`❌ 请求失败: ${error.message}`);
    }
  }, 3000); // 每3秒发送一次请求

  // 测试结束
  setTimeout(() => {
    clearInterval(interval);
    
    const elapsed = Date.now() - startTime;
    console.log('\n📊 测试结果:');
    console.log(`   测试时长: ${elapsed}ms`);
    console.log(`   总请求数: ${requestCount}`);
    console.log(`   平均请求间隔: ${elapsed / requestCount}ms`);
    
    if (requestCount <= maxAllowedRequests) {
      console.log('\n✅ 测试通过！');
      console.log('   API请求频率正常，无无限循环问题');
      process.exit(0);
    } else {
      console.log('\n❌ 测试失败！');
      console.log('   检测到异常高频的API请求');
      process.exit(1);
    }
  }, testDuration);
}

// 额外测试：检查前端页面是否正常加载
async function testPageLoad() {
  console.log('🌐 测试前端页面加载...');
  
  try {
    // 测试前端页面是否可访问
    const response = await axios.get('http://localhost:3005/admin/reviews', {
      timeout: 10000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; TestBot/1.0)'
      }
    });
    
    if (response.status === 200) {
      console.log('✅ 前端页面加载正常');
    } else {
      console.log(`⚠️ 前端页面响应状态: ${response.status}`);
    }
  } catch (error) {
    console.log(`❌ 前端页面加载失败: ${error.message}`);
  }
}

// 主函数
async function main() {
  console.log('🚀 无限循环修复验证测试');
  console.log('================================\n');
  
  // 测试页面加载
  await testPageLoad();
  
  console.log('\n--------------------------------\n');
  
  // 监控API请求
  await monitorApiRequests();
}

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('测试运行出错:', error);
    process.exit(1);
  });
}

module.exports = {
  monitorApiRequests,
  testPageLoad
};
