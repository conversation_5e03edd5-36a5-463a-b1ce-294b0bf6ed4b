-- 验证套餐付款相关表结构的SQL脚本

-- 检查package_payments表是否存在
SELECT 'package_payments表存在' as status 
FROM information_schema.tables 
WHERE table_schema = 'hotel_booking' 
AND table_name = 'package_payments';

-- 检查package_payments表结构
DESCRIBE package_payments;

-- 检查package_bookings表是否有付款相关字段
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM information_schema.COLUMNS 
WHERE table_schema = 'hotel_booking' 
AND table_name = 'package_bookings'
AND COLUMN_NAME IN ('payment_status', 'payment_deadline', 'paid_amount', 'refund_amount', 'payment_notes', 'last_payment_at');

-- 检查package_bookings表的所有字段
DESCRIBE package_bookings;

-- 检查是否有测试数据
SELECT COUNT(*) as package_bookings_count FROM package_bookings;
SELECT COUNT(*) as package_payments_count FROM package_payments;

-- 如果有数据，显示一些示例
SELECT id, booking_number, package_id, status, payment_status, total_amount, paid_amount 
FROM package_bookings 
LIMIT 5;

SELECT id, payment_number, package_booking_id, amount, payment_method, status, created_at
FROM package_payments 
LIMIT 5;
