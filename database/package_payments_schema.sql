-- 文化套餐付款功能数据库结构
-- 创建时间: 2025-01-07
-- 描述: 为文化套餐预订系统添加完整的付款功能支持

-- 1. 创建套餐付款记录表
CREATE TABLE package_payments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '付款记录ID',
    payment_number VARCHAR(50) NOT NULL UNIQUE COMMENT '付款编号',
    package_booking_id BIGINT NOT NULL COMMENT '套餐预订ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 付款金额信息
    amount DECIMAL(10,2) NOT NULL COMMENT '付款金额',
    currency VARCHAR(3) DEFAULT 'CNY' COMMENT '货币类型',
    
    -- 付款方式和状态
    payment_method ENUM('ALIPAY', 'WECHAT_PAY', 'CREDIT_CARD', 'BANK_CARD', 'CASH', 'BANK_TRANSFER') NOT NULL COMMENT '付款方式',
    status ENUM('PENDING', 'PROCESSING', 'SUCCESS', 'FAILED', 'CANCELLED', 'REFUNDED', 'PARTIAL_REFUND') DEFAULT 'PENDING' COMMENT '付款状态',
    
    -- 第三方支付信息
    gateway_type VARCHAR(20) COMMENT '支付网关类型',
    transaction_id VARCHAR(100) COMMENT '第三方交易ID',
    gateway_order_id VARCHAR(100) COMMENT '网关订单号',
    gateway_response TEXT COMMENT '网关响应信息',
    
    -- 付款处理信息
    processed_at TIMESTAMP NULL COMMENT '处理完成时间',
    failure_reason VARCHAR(500) COMMENT '失败原因',
    refund_reason VARCHAR(500) COMMENT '退款原因',
    refund_amount DECIMAL(10,2) DEFAULT 0 COMMENT '退款金额',
    refund_at TIMESTAMP NULL COMMENT '退款时间',
    
    -- 安全和验证信息
    client_ip VARCHAR(45) COMMENT '客户端IP地址',
    user_agent TEXT COMMENT '用户代理信息',
    payment_token VARCHAR(255) COMMENT '付款令牌',
    signature VARCHAR(255) COMMENT '签名验证',
    
    -- 超时和重试信息
    expires_at TIMESTAMP NULL COMMENT '付款过期时间',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    max_retry_count INT DEFAULT 3 COMMENT '最大重试次数',
    
    -- 审计字段
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    
    -- 外键约束
    FOREIGN KEY (package_booking_id) REFERENCES package_bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_payment_number (payment_number),
    INDEX idx_package_booking_id (package_booking_id),
    INDEX idx_user_id (user_id),
    INDEX idx_payment_method (payment_method),
    INDEX idx_status (status),
    INDEX idx_gateway_type (gateway_type),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_created_at (created_at),
    INDEX idx_processed_at (processed_at),
    INDEX idx_expires_at (expires_at),
    INDEX idx_status_created (status, created_at),
    INDEX idx_user_status (user_id, status),
    INDEX idx_booking_status (package_booking_id, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐付款记录表';

-- 2. 扩展package_bookings表的付款相关字段
-- 检查并添加付款截止时间字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'ganzi_hotel_booking'
    AND TABLE_NAME = 'package_bookings'
    AND COLUMN_NAME = 'payment_deadline') = 0,
    'ALTER TABLE package_bookings ADD COLUMN payment_deadline TIMESTAMP NULL COMMENT ''付款截止时间'' AFTER payment_status',
    'SELECT ''payment_deadline column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加已付金额字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'ganzi_hotel_booking'
    AND TABLE_NAME = 'package_bookings'
    AND COLUMN_NAME = 'paid_amount') = 0,
    'ALTER TABLE package_bookings ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0 COMMENT ''已付金额'' AFTER total_amount',
    'SELECT ''paid_amount column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加退款金额字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'ganzi_hotel_booking'
    AND TABLE_NAME = 'package_bookings'
    AND COLUMN_NAME = 'refund_amount') = 0,
    'ALTER TABLE package_bookings ADD COLUMN refund_amount DECIMAL(10,2) DEFAULT 0 COMMENT ''退款金额'' AFTER paid_amount',
    'SELECT ''refund_amount column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加付款备注字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'ganzi_hotel_booking'
    AND TABLE_NAME = 'package_bookings'
    AND COLUMN_NAME = 'payment_notes') = 0,
    'ALTER TABLE package_bookings ADD COLUMN payment_notes TEXT COMMENT ''付款备注'' AFTER special_requirements',
    'SELECT ''payment_notes column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加最后付款时间字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'ganzi_hotel_booking'
    AND TABLE_NAME = 'package_bookings'
    AND COLUMN_NAME = 'last_payment_at') = 0,
    'ALTER TABLE package_bookings ADD COLUMN last_payment_at TIMESTAMP NULL COMMENT ''最后付款时间'' AFTER updated_at',
    'SELECT ''last_payment_at column already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 创建付款日志表（用于审计和追踪）
CREATE TABLE package_payment_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    payment_id BIGINT NOT NULL COMMENT '付款记录ID',
    action VARCHAR(50) NOT NULL COMMENT '操作类型',
    old_status VARCHAR(20) COMMENT '原状态',
    new_status VARCHAR(20) COMMENT '新状态',
    description TEXT COMMENT '操作描述',
    operator_id BIGINT COMMENT '操作人ID',
    operator_type ENUM('USER', 'ADMIN', 'SYSTEM') DEFAULT 'SYSTEM' COMMENT '操作人类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (payment_id) REFERENCES package_payments(id) ON DELETE CASCADE,
    
    INDEX idx_payment_id (payment_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_operator (operator_id, operator_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐付款操作日志表';

-- 4. 创建付款配置表
CREATE TABLE package_payment_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') DEFAULT 'STRING' COMMENT '配置类型',
    description VARCHAR(500) COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='套餐付款配置表';

-- 5. 插入默认付款配置
INSERT INTO package_payment_config (config_key, config_value, config_type, description) VALUES
('payment.timeout.minutes', '30', 'NUMBER', '付款超时时间（分钟）'),
('payment.max.retry.count', '3', 'NUMBER', '最大重试次数'),
('payment.min.amount', '0.01', 'NUMBER', '最小付款金额'),
('payment.max.amount', '50000.00', 'NUMBER', '最大付款金额'),
('payment.alipay.enabled', 'true', 'BOOLEAN', '是否启用支付宝'),
('payment.wechat.enabled', 'true', 'BOOLEAN', '是否启用微信支付'),
('payment.credit.card.enabled', 'true', 'BOOLEAN', '是否启用信用卡'),
('payment.bank.card.enabled', 'true', 'BOOLEAN', '是否启用银行卡'),
('payment.refund.auto.enabled', 'false', 'BOOLEAN', '是否启用自动退款'),
('payment.notification.enabled', 'true', 'BOOLEAN', '是否启用付款通知');

-- 6. 创建视图：付款统计视图
CREATE VIEW v_package_payment_statistics AS
SELECT 
    DATE(pp.created_at) as payment_date,
    pp.payment_method,
    pp.status,
    COUNT(*) as payment_count,
    SUM(pp.amount) as total_amount,
    AVG(pp.amount) as avg_amount,
    MIN(pp.amount) as min_amount,
    MAX(pp.amount) as max_amount
FROM package_payments pp
WHERE pp.created_at >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(pp.created_at), pp.payment_method, pp.status
ORDER BY payment_date DESC, payment_method, status;

-- 7. 创建触发器：自动更新预订的付款状态
DELIMITER //
CREATE TRIGGER tr_update_booking_payment_status 
AFTER UPDATE ON package_payments
FOR EACH ROW
BEGIN
    DECLARE total_paid DECIMAL(10,2) DEFAULT 0;
    DECLARE booking_total DECIMAL(10,2) DEFAULT 0;
    DECLARE new_payment_status VARCHAR(20);
    
    -- 计算该预订的总付款金额
    SELECT COALESCE(SUM(amount), 0) INTO total_paid
    FROM package_payments 
    WHERE package_booking_id = NEW.package_booking_id 
    AND status = 'SUCCESS';
    
    -- 获取预订总金额
    SELECT total_amount INTO booking_total
    FROM package_bookings 
    WHERE id = NEW.package_booking_id;
    
    -- 确定新的付款状态
    IF total_paid = 0 THEN
        SET new_payment_status = 'PENDING';
    ELSEIF total_paid >= booking_total THEN
        SET new_payment_status = 'PAID';
    ELSE
        SET new_payment_status = 'PARTIAL';
    END IF;
    
    -- 更新预订的付款状态和已付金额
    UPDATE package_bookings 
    SET 
        payment_status = new_payment_status,
        paid_amount = total_paid,
        last_payment_at = CASE WHEN NEW.status = 'SUCCESS' THEN NEW.processed_at ELSE last_payment_at END
    WHERE id = NEW.package_booking_id;
END//
DELIMITER ;
