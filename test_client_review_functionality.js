/**
 * 测试客户端评价功能
 * 验证用户可以正常进行评价操作
 */

const puppeteer = require('puppeteer');

async function testClientReviewFunctionality() {
    console.log('🚀 开始测试客户端评价功能...');
    
    const browser = await puppeteer.launch({ 
        headless: false,
        defaultViewport: null,
        args: ['--start-maximized']
    });
    
    const page = await browser.newPage();
    
    try {
        // 1. 访问酒店详情页面
        console.log('📍 访问酒店详情页面...');
        await page.goto('http://localhost:3003/hotels/16', { waitUntil: 'networkidle2' });
        
        // 等待页面加载
        await page.waitForTimeout(3000);
        
        // 2. 检查是否显示登录提示
        console.log('🔍 检查登录状态...');
        const loginButton = await page.$('button:contains("立即登录")');
        if (loginButton) {
            console.log('✅ 未登录用户看到登录提示');
            
            // 点击登录按钮
            await loginButton.click();
            await page.waitForTimeout(2000);
            
            // 填写登录信息
            await page.type('input[placeholder*="用户名"]', 'testuser2025');
            await page.type('input[placeholder*="密码"]', 'password123');
            
            // 点击登录
            await page.click('button[type="submit"]');
            await page.waitForTimeout(3000);
            
            console.log('✅ 登录成功');
        }
        
        // 3. 返回酒店详情页面
        await page.goto('http://localhost:3003/hotels/16', { waitUntil: 'networkidle2' });
        await page.waitForTimeout(3000);
        
        // 4. 切换到评价标签页
        console.log('📝 切换到评价标签页...');
        const reviewTab = await page.$('div[data-node-key="reviews"]');
        if (reviewTab) {
            await reviewTab.click();
            await page.waitForTimeout(2000);
        }
        
        // 5. 检查评价权限
        console.log('🔍 检查评价权限...');
        const canReviewButton = await page.$('button:contains("写评价")');
        const alreadyReviewedText = await page.$('text:contains("您已经评价过此酒店")');
        
        if (canReviewButton) {
            console.log('✅ 用户可以评价酒店');
            
            // 点击写评价按钮
            await canReviewButton.click();
            await page.waitForTimeout(2000);
            
            // 检查评价表单是否打开
            const reviewModal = await page.$('.ant-modal');
            if (reviewModal) {
                console.log('✅ 评价表单打开成功');
                
                // 关闭表单
                const cancelButton = await page.$('.ant-modal button:contains("取消")');
                if (cancelButton) {
                    await cancelButton.click();
                }
            }
        } else if (alreadyReviewedText) {
            console.log('✅ 用户已经评价过此酒店，显示正确提示');
        } else {
            console.log('❌ 评价功能状态异常');
        }
        
        // 6. 检查评价列表
        console.log('📋 检查评价列表...');
        const reviewList = await page.$('.ant-list');
        if (reviewList) {
            const reviewItems = await page.$$('.ant-list-item');
            console.log(`✅ 找到 ${reviewItems.length} 条评价`);
            
            if (reviewItems.length > 0) {
                console.log('✅ 评价列表显示正常');
            }
        } else {
            const emptyState = await page.$('.ant-empty');
            if (emptyState) {
                console.log('✅ 暂无评价，显示空状态');
            }
        }
        
        // 7. 检查评价统计
        console.log('📊 检查评价统计...');
        const statisticsCard = await page.$('.ant-card');
        if (statisticsCard) {
            console.log('✅ 评价统计卡片显示正常');
        }
        
        console.log('🎉 客户端评价功能测试完成！');
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error);
    } finally {
        await browser.close();
    }
}

// 运行测试
testClientReviewFunctionality().catch(console.error);
