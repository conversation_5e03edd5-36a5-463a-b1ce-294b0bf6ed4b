/**
 * 调试付款模态框的演示模式
 */

console.log('🔧 调试付款模态框演示模式');
console.log('==============================');

// 模拟测试数据
const testBookingData = {
  id: null, // 关键：设置为null来触发演示模式
  bookingNumber: 'PKG20241206001',
  packageName: '甘孜藏族文化体验套餐',
  totalAmount: 1998.00,
  finalAmount: 1998.00,
  participantCount: 2,
  bookingDate: '2024-12-15',
  contactName: '张三',
  contactPhone: '13800138000',
  status: 'CONFIRMED',
  paymentStatus: 'PENDING'
};

console.log('📋 测试数据:');
console.log('bookingData.id =', testBookingData.id);
console.log('typeof bookingData.id =', typeof testBookingData.id);
console.log('!bookingData?.id =', !testBookingData?.id);
console.log('bookingData?.id === null =', testBookingData?.id === null);
console.log('bookingData?.id === undefined =', testBookingData?.id === undefined);

// 模拟演示模式检测逻辑
function checkDemoMode(bookingData) {
  console.log('\n🔍 演示模式检测:');
  console.log('输入数据:', bookingData);
  console.log('bookingData?.id =', bookingData?.id);
  
  if (!bookingData?.id) {
    console.log('✅ 进入演示模式');
    return true;
  } else {
    console.log('❌ 进入真实模式，将调用API');
    return false;
  }
}

// 测试不同的数据情况
const testCases = [
  { name: '空ID (null)', data: { ...testBookingData, id: null } },
  { name: '未定义ID (undefined)', data: { ...testBookingData, id: undefined } },
  { name: '空字符串ID', data: { ...testBookingData, id: '' } },
  { name: '数字0', data: { ...testBookingData, id: 0 } },
  { name: '真实ID', data: { ...testBookingData, id: 4 } },
  { name: '字符串ID', data: { ...testBookingData, id: '4' } }
];

console.log('\n🧪 测试不同ID值的演示模式检测:');
console.log('=====================================');

testCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. ${testCase.name}:`);
  const isDemoMode = checkDemoMode(testCase.data);
  console.log(`   结果: ${isDemoMode ? '演示模式' : '真实模式'}`);
});

// 模拟API调用检测
function simulateAPICall(bookingData) {
  console.log('\n🌐 模拟API调用:');
  
  if (!bookingData?.id) {
    console.log('🎭 演示模式：创建模拟响应');
    const mockResponse = {
      success: true,
      data: {
        paymentNumber: `PAY${Date.now()}`,
        amount: bookingData.totalAmount,
        status: 'PENDING',
        message: '演示模式 - 请选择付款方式体验界面效果'
      }
    };
    console.log('模拟响应:', mockResponse);
    return mockResponse;
  } else {
    console.log('🔗 真实模式：将调用 packagePaymentService.createPayment');
    console.log(`API URL: /packages/bookings/${bookingData.id}/payment`);
    return null;
  }
}

console.log('\n🎯 最终测试:');
console.log('=============');
simulateAPICall(testBookingData);

console.log('\n💡 调试建议:');
console.log('=============');
console.log('1. 检查浏览器控制台是否显示 "🔍 Debug: bookingData?.id = null"');
console.log('2. 检查是否显示 "🎭 进入演示模式"');
console.log('3. 如果仍然调用API，检查是否有其他地方覆盖了bookingData');
console.log('4. 清除浏览器缓存和localStorage');
console.log('5. 确保没有其他标签页在使用真实的预订数据');

console.log('\n🚀 下一步操作:');
console.log('===============');
console.log('1. 打开浏览器开发者工具');
console.log('2. 访问 http://localhost:3006/test-payment');
console.log('3. 点击付款按钮');
console.log('4. 查看控制台输出');
console.log('5. 选择任意付款方式并提交');

console.log('\n✅ 预期结果:');
console.log('=============');
console.log('- 控制台显示: 🔍 Debug: bookingData?.id = null');
console.log('- 控制台显示: 🎭 进入演示模式');
console.log('- 显示演示模式提示信息');
console.log('- 不会出现API 400错误');
console.log('- 付款流程正常完成');
